<div class="calendar-container">
    <!-- Calendar Navigation -->
    <div class="calendar-header d-flex justify-content-between align-items-center mb-4">
        <button type="button" class="btn btn-outline-primary" id="prevMonth">
            <i class="fa fa-chevron-left"></i>
        </button>
        <h4 id="currentMonth" class="mb-0"></h4>
        <button type="button" class="btn btn-outline-primary" id="nextMonth">
            <i class="fa fa-chevron-right"></i>
        </button>
    </div>

    <!-- Calendar Days -->
    <div id="calendarDays" class="calendar-days">
        <!-- Days will be loaded here -->
    </div>

    <!-- Close Button -->
    <div class="text-right mt-3">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">
            <i class="fa fa-times"></i> Tutup
        </button>
    </div>
</div>

<style>
.calendar-container {
    padding: 20px;
}

.calendar-days {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 20px;
}

.day-card {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    min-height: 400px;
}

.day-header {
    background: #007bff;
    color: white;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 15px;
    font-weight: 600;
}

.day-date {
    font-size: 1.2rem;
    margin-bottom: 5px;
}

.day-name {
    font-size: 0.9rem;
    opacity: 0.9;
}

.slots-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-top: 15px;
}

.slot-box {
    border: 2px dashed #ccc;
    border-radius: 6px;
    padding: 10px;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.slot-box.empty {
    background: #f8f9fa;
    border-color: #28a745;
}

.slot-box.empty:hover {
    background: #e8f5e9;
    border-color: #1e7e34;
}

.slot-box.occupied {
    background: #fff3cd;
    border: 2px solid #ffc107;
    border-style: solid;
}

.slot-box.occupied:hover {
    background: #ffeaa7;
}

.slot-number {
    position: absolute;
    top: 5px;
    left: 8px;
    font-size: 0.8rem;
    font-weight: 600;
    color: #666;
}

.slot-content {
    text-align: center;
    font-size: 0.85rem;
    line-height: 1.3;
}

.slot-content .patient-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 3px;
}

.slot-content .doctor-name {
    color: #007bff;
    margin-bottom: 3px;
}

.slot-content .diagnosis {
    color: #666;
    font-size: 0.8rem;
    margin-bottom: 3px;
}

.slot-content .time {
    color: #28a745;
    font-weight: 600;
    font-size: 0.8rem;
}

.slot-add-icon {
    font-size: 2rem;
    color: #28a745;
    margin-bottom: 5px;
}

.slot-label {
    font-size: 0.9rem;
    color: #666;
    font-weight: 500;
}

.kamar-section {
    margin-bottom: 20px;
}

.kamar-title {
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 6px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 15px;
    border-left: 4px solid #007bff;
}

@media (max-width: 768px) {
    .calendar-days {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .slots-container {
        grid-template-columns: 1fr;
        gap: 8px;
    }
    
    .slot-box {
        min-height: 100px;
        padding: 8px;
    }
    
    .slot-content {
        font-size: 0.8rem;
    }
}
</style>

<script>
$(document).ready(function() {
    var currentDate = new Date();
    var currentMonth = currentDate.getMonth();
    var currentYear = currentDate.getFullYear();
    
    // Indonesian month names
    var monthNames = [
        'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
        'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
    ];
    
    // Indonesian day names
    var dayNames = ['Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu'];
    
    function loadCalendar() {
        $('#currentMonth').text(monthNames[currentMonth] + ' ' + currentYear);
        
        // Calculate date range (show 30 days from current date)
        var startDate = new Date(currentYear, currentMonth, 1);
        var endDate = new Date(currentYear, currentMonth + 1, 0);
        
        // Adjust to show only next 30 days
        var today = new Date();
        startDate = new Date(Math.max(startDate.getTime(), today.getTime()));
        endDate = new Date(startDate.getTime() + (30 * 24 * 60 * 60 * 1000));
        
        loadCalendarData(startDate, endDate);
    }
    
    function loadCalendarData(startDate, endDate) {
        $.ajax({
            url: '<?php echo base_url('PenjadwalanOperasi/get_calendar_data'); ?>',
            type: 'POST',
            data: {
                start_date: startDate.toISOString().split('T')[0],
                end_date: endDate.toISOString().split('T')[0]
            },
            dataType: 'json',
            success: function(response) {
                renderCalendar(startDate, endDate, response);
            },
            error: function() {
                Swal.fire('Error', 'Gagal memuat data kalender', 'error');
            }
        });
    }
    
    function renderCalendar(startDate, endDate, data) {
        var calendarHtml = '';
        var currentDate = new Date(startDate);
        var dayCount = 0;
        
        while (currentDate <= endDate && dayCount < 21) { // Show max 21 days (3 weeks)
            if (currentDate.getDay() >= 1 && currentDate.getDay() <= 5) { // Monday to Friday only
                var dateStr = currentDate.toISOString().split('T')[0];
                var dayData = data[dateStr] || {};
                
                calendarHtml += renderDayCard(currentDate, dayData);
                dayCount++;
            }
            currentDate.setDate(currentDate.getDate() + 1);
        }
        
        $('#calendarDays').html(calendarHtml);
    }
    
    function renderDayCard(date, dayData) {
        var dayName = dayNames[date.getDay()];
        var dayDate = date.getDate();
        var dateStr = date.toISOString().split('T')[0];
        
        var html = '<div class="day-card">';
        html += '<div class="day-header">';
        html += '<div class="day-name">' + dayName + '</div>';
        html += '<div class="day-date">' + dayDate + '</div>';
        html += '</div>';
        
        // OKA 1 (Slots 1-4)
        html += '<div class="kamar-section">';
        html += '<div class="kamar-title">OKA 1</div>';
        html += '<div class="slots-container">';
        for (var i = 1; i <= 4; i++) {
            html += renderSlot(dateStr, i, dayData['oka1_slot' + i] || null, 'OKA 1');
        }
        html += '</div></div>';
        
        // OKA 2 (Slots 5-8)
        html += '<div class="kamar-section">';
        html += '<div class="kamar-title">OKA 2</div>';
        html += '<div class="slots-container">';
        for (var i = 5; i <= 8; i++) {
            html += renderSlot(dateStr, i, dayData['oka2_slot' + (i-4)] || null, 'OKA 2');
        }
        html += '</div></div>';
        
        html += '</div>';
        return html;
    }
    
    function renderSlot(date, slotNumber, slotData, kamar) {
        var html = '<div class="slot-box ' + (slotData ? 'occupied' : 'empty') + '" ';
        html += 'data-date="' + date + '" data-slot="' + slotNumber + '" data-kamar="' + kamar + '">';
        html += '<div class="slot-number">Slot ' + (slotNumber <= 4 ? slotNumber : slotNumber - 4) + '</div>';
        
        if (slotData) {
            html += '<div class="slot-content">';
            html += '<div class="patient-name">' + slotData.pasien_info + '</div>';
            html += '<div class="doctor-name">' + slotData.nama_dokter + '</div>';
            html += '<div class="diagnosis">' + slotData.rencana_tindakan_operasi + '</div>';
            html += '<div class="time">' + slotData.waktu_selesai + '</div>';
            html += '</div>';
        } else {
            html += '<div class="slot-add-icon">+</div>';
            html += '<div class="slot-label">Slot Kosong</div>';
        }
        
        html += '</div>';
        return html;
    }
    
    // Event handlers
    $('#prevMonth').click(function() {
        currentMonth--;
        if (currentMonth < 0) {
            currentMonth = 11;
            currentYear--;
        }
        loadCalendar();
    });
    
    $('#nextMonth').click(function() {
        currentMonth++;
        if (currentMonth > 11) {
            currentMonth = 0;
            currentYear++;
        }
        loadCalendar();
    });
    
    // Slot click handler
    $(document).on('click', '.slot-box.empty', function() {
        var date = $(this).data('date');
        var slot = $(this).data('slot');
        var kamar = $(this).data('kamar');
        
        // Set selected date and slot in the form
        $('#tanggalOperasiPenjadwalan').val(date);
        // You can also set other fields like kamar_operasi, slot_operasi, etc.
        
        Swal.fire({
            title: 'Slot Dipilih',
            text: 'Tanggal: ' + date + ', ' + kamar + ', Slot ' + (slot <= 4 ? slot : slot - 4),
            icon: 'success',
            timer: 1500,
            showConfirmButton: false
        }).then(() => {
            $('#modalPilihTanggalOperasi').modal('hide');
        });
    });
    
    $(document).on('click', '.slot-box.occupied', function() {
        Swal.fire('Info', 'Slot ini sudah terisi', 'info');
    });
    
    // Initialize calendar
    loadCalendar();
});
</script>
