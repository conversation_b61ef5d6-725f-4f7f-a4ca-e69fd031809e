<style>
    .select2-container--default .select2-selection--single.is-invalid {
        border-color: #dc3545 !important;
        border-width: 1px;
    }
</style>
<!-- <div class="modal fade" id="IndexReservasiEdit" tabindex="-1" role="dialog"> -->
<div class="modal fade" id="<?= $statusForm == 1 ? 'IndexReservasiEdit1' : 'IndexReservasiEdit2' ?>" tabindex="-1">
    <div class="modal-dialog  modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h4 class="title" id="defaultModalLabel">Edit Reservasi</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="formReservasiEdit" method="POST">
            <div class="modal-body">
                <input type="hidden" id="idReservasi" name="idReservasi" value="<?= $id; ?>">
                <input type="hidden" id="id_perjanjian" name="id_perjanjian" value="<?= $id_perjanjian; ?>">
                 <input type="hidden" name="statusForm " value="<?= $statusForm  ?? ''; ?>">
                
                

                <div class="row">
                    <!-- Kolom kiri -->
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="kode_booking">Kode Booking</label>
                            <input type="text" id="kode_booking" name="kode_booking" class="form-control validate-input" value="<?= $KODE_BOOKING; ?>" readonly>
                        </div>
                        <div class="form-group">
                            <label for="nomr">Nomr</label>
                            <input type="text" id="nomr" name="nomr"  readonly class="form-control validate-input" value="<?= $NORM; ?>">
                        </div>
                        <div class="form-group">
                            <label for="nama">Nama</label>
                            <input type="text" id="nama" name="nama" class="form-control validate-input" value="<?= $PASIEN; ?>" readonly>
                        </div>
                        <div class="form-group">
                            <label for="jenis_kelamin">Jenis Kelamin</label>
                            <input type="text" id="jenis_kelamin" name="jenis_kelamin" class="form-control validate-input" value="<?= $JK; ?>" readonly>
                        </div>
                        <div class="form-group">
                            <label for="tanggal_lahir">Tanggal Lahir</label>
                            <input type="text" id="tanggal_lahir" name="tanggal_lahir" class="form-control validate-input" value="<?= $TANGGAL_LAHIR; ?>" readonly>
                        </div>
                        <div class="form-group">
                            <label for="no_telp">No. Telephone</label>
                            <input type="text" id="no_telp" name="no_telp" class="form-control validate-input" value="<?= $NOHP; ?>" readonly>
                        </div>                       
                        <div class="form-group">
                            <label for="tgl_rencana_masuk">Tanggal Rencana Masuk</label>
                            <input type="date" id="tgl_rencana_masuk" name="tgl_rencana_masuk" class="form-control validate-input" value="<?= $TGL_RENCANA; ?>">
                            <input type="hidden" id="tgl_rencana" name="tgl_rencana" class="form-control validate-input" value="<?= $TGL_RENCANA; ?>">
                            <input type="hidden" id="tgl_rencana_masuk_old" name="tgl_rencana_masuk_old" value="<?= $TGL_RENCANA; ?>">
                        </div>
                        <div class="form-group">
                            <label for="tgl_pcr">Tanggal PCR</label>
                            <input type="date" id="tgl_pcr" name="tgl_pcr" class="form-control" value="<?= $TGL_PCR; ?>">
                        </div>
                        <?php if(isset($statusForm) && $statusForm == 1): ?>
                            <div class="form-group">
                                <label for="nama_kamar">Kamar</label>
                                <input type="text" id="nama_kamar" name="nama_kamar" class="form-control" value="<?= $nama_kamar ?? ''; ?>" readonly>
                                <input type="hidden" name="RE_SIMPEL" value="<?= $RE_SIMPEL ?? ''; ?>">

                               

                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Kolom kanan -->
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="diagnosa">Diagnosa</label>
                            <textarea id="diagnosa" name="diagnosa" class="form-control"><?= $DIAGNOSA; ?></textarea>
                        </div>
                        <div class="form-group">
                            <label for="keterangan">Keterangan</label>
                            <textarea id="keterangan" name="keterangan" class="form-control"><?= $KETERANGAN; ?></textarea>
                        </div>
                        <div class="form-group">
                            <label for="cara_bayar">Cara Bayar</label>
                            <input type="text" class="form-control validate-input" name="cara_bayar" id="cara_bayar" value="<?= $BAYAR; ?>">
                            <input type="hidden" name="idCara_bayar" id="idCara_bayar" value="<?= $ID_BAYAR; ?>">
                        </div>
                        <div class="form-group">
                            <label for="kelasPasien">Kelas</label>
                            <select class="form-control validate-input" name="kelasPasien" id="kelasPasien">
                            <?php foreach ($list_kelas as $kelas): ?>
                                <option value="<?= ($kelas['ID']); ?>" 
                                    <?= isset($KELAS) && $KELAS == $kelas['ID'] ? 'selected' : ''; ?>>
                                    <?= ($kelas['DESKRIPSI']); ?>
                                </option>
                            <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="dokter">Dokter</label>
                            <select class="form-control validate-input" style="width: 100%" name="dokter" id="dokter">
                            <?php foreach ($list_dokter as $dokter): ?>
                                <option value="<?= html_escape($dokter['ID']); ?>" 
                                    <?= isset($DOKTER) && $DOKTER == $dokter['ID'] ? 'selected' : ''; ?>>
                                    <?= html_escape($dokter['DOKTER']); ?>
                                </option>
                            <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="tujuan_dirawat">Tujuan Dirawat</label>
                            <select class="form-control validate-input" style="width: 100%" name="tujuan_dirawat" id="tujuan_dirawat">
                            <?php foreach ($list_tujuan as $tujuan): ?>
                                <option value="<?= ($tujuan['ID']); ?>" 
                                    <?= isset($ID_RAWAT) && $ID_RAWAT == $tujuan['ID'] ? 'selected' : ''; ?>>
                                    <?= ($tujuan['DESKRIPSI']); ?>
                                </option>
                            <?php endforeach; ?>
                            </select>
                        </div>  
                        <div class="form-group">
                            <label for="alasan_edit">Alasan Perubahan</label>
                            <div class="d-flex align-items-center mb-2">
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" id="isPasienRequest" name="isPasienRequest">
                                    <label class="custom-control-label" for="isPasienRequest" data-toggle="tooltip" data-placement="right" title="Centang jika perubahan atas permintaan pasien">Permintaan dari Pasien</label>
                                </div>
                            </div>
                            <textarea name="alasan_edit" class="form-control validate-input" placeholder="Alasan Perubahan"></textarea>
                        </div>                         
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-primary" id="updateRE">Simpan</button>
                <button type="button" class="btn btn-primary" id="updateKirim">Simpan & Kirim</button>
            </div>
        </form>

        </div>
    </div>
</div>




<script>
    $(document).ready(function(){
        $('[data-toggle="tooltip"]').tooltip();
        $('#dokter').select2({
            placeholder:"[ Pilih Dokter ]",
            width: '100%'
        });
        $("#cara_bayar").autocomplete({
            serviceUrl: "<?= base_url('Admision/cara_bayar'); ?>", 
            paramName: "q", 
            dataType: "json",
            minChars: 1,
            transformResult: function (response) {
                return {
                    suggestions: $.map(response, function (dataItem) {
                        return {
                            value: dataItem.value, 
                            data: dataItem.id  
                        };
                    })
                };
            },
            onSelect: function (suggestion) {
                $("#idCara_bayar").val(suggestion.data);
            }
        });
        $('#kelasPasien').select2({
            placeholder: '[ Pilih Kelas]',
            width: '100%'
        });
        $('#tujuan_dirawat').select2({
            placeholder:"[ Pilih Tujuan Pasien ]",
            width: '100%'
           
        });
        // $('#updateKirim').on('click', function(){
        //     let isValid = true;          
        //     $('#tgl_rencana_masuk, #cara_bayar, #kelasPasien, #dokter, #tujuan_dirawat, textarea[name="alasan_edit"]').each(function() {
        //     // Menghapus status invalid dan feedback sebelumnya
        //         $(this).removeClass('is-invalid');
        //         if ($(this).hasClass('select2-hidden-accessible')) {
        //             // select2
        //             if (!$(this).val()) {
        //                 isValid = false;
        //                 $(this).next('.select2').find('.select2-selection').addClass('is-invalid');
        //                 if ($(this).next('.select2').next('.invalid-feedback').length === 0) {
        //                     $(this).next('.select2').after('<div class="invalid-feedback d-block">Field ini wajib dipilih.</div>');
        //                 }
        //             } else {
        //                 $(this).next('.select2').find('.select2-selection').removeClass('is-invalid');
        //                 $(this).closest('.form-group').find('.invalid-feedback').remove();
        //             }
        //         } else {
        //             if ($(this).val().trim() === '') {
        //                 isValid = false;
        //                 $(this).addClass('is-invalid');
        //                 if ($(this).next('.invalid-feedback').length === 0) {
        //                     $(this).after('<div class="invalid-feedback">Field ini wajib diisi.</div>');
        //                 }
        //             } else {
        //                 $(this).removeClass('is-invalid');
        //                 $(this).next('.invalid-feedback').remove();
        //             }
        //         }
        //     });  

        //     if (!isValid) {
        //         Swal.fire({
        //             title: "Error!",
        //             text: "terdapat data yang kosong, tolong lengkapi.",
        //             type: 'error'
        //         });
        //         return;
        //     }
        //     let formData = $('#formReservasiEdit').serialize();
        //     formData += '&send_wa=1'; 
        //     Swal.fire({
        //     title: "Yakin Ingin Simpan?",
        //     text: "Data akan disimpan",
        //     type: 'warning',
        //     showConfirmButton:true,
        //     showCancelButton: true,
        //     confirmButtonText: 'Ya',
        //     cancelButtonText: 'Batal',
        //     focusConfirm:true
        //     }).then(result => {
        //         if (result.value) {
        //             // var form = $("#formReservasiEdit").serializeArray(); //array bila data mau dimanupulasi di frontend
        //             $.ajax({
        //                 dataType:'json',
        //                 url: "<?php echo base_url('Admision/updateReservasi')?>",
        //                 type: 'POST',
        //                 data: formData,
        //                 success: function(data){
        //                     if(data.status == 'success'){
        //                         Swal.fire({
        //                             title: "Data berhasil dirubah",
        //                             type: 'success',
        //                         }).then(function() {
        //                             location.reload();
        //                         }, 1000);
        //                     } else {
        //                         Swal.fire({
        //                             title: "Error!",
        //                             text: data.message, 
        //                             type: 'error'
        //                         });
        //                     }
        //                 },
        //                 error: function(jqXHR, textStatus, errorThrown) {
        //                     Swal.fire({
        //                         title: "Error!",
        //                         text: "Terjadi kesalahan dalam mengirim permintaan.",
        //                         type: 'error'
        //                     });
        //                 }
        //             });
        //         } else if(result.dismiss === Swal.DismissReason.cancel){
        //             Swal.fire("Data Tidak Tersimpan!", '', 'info');
        //         }
        //     });
        // });
        // $('#updateRE').on('click', function(){
        //     let isValid = true;          
        //     $('#tgl_rencana_masuk, #cara_bayar, #kelasPasien, #dokter, #tujuan_dirawat, textarea[name="alasan_edit"]').each(function() {
        //         $(this).removeClass('is-invalid');
        //         if ($(this).hasClass('select2-hidden-accessible')) {
        //             // select2
        //             if (!$(this).val()) {
        //                 isValid = false;
        //                 $(this).next('.select2').find('.select2-selection').addClass('is-invalid');
        //                 if ($(this).next('.select2').next('.invalid-feedback').length === 0) {
        //                     $(this).next('.select2').after('<div class="invalid-feedback d-block">Field ini wajib dipilih.</div>');
        //                 }
        //             } else {
        //                 $(this).next('.select2').find('.select2-selection').removeClass('is-invalid');
        //                 $(this).closest('.form-group').find('.invalid-feedback').remove();
        //             }
        //         } else {
        //             if ($(this).val().trim() === '') {
        //                 isValid = false;
        //                 $(this).addClass('is-invalid');
        //                 if ($(this).next('.invalid-feedback').length === 0) {
        //                     $(this).after('<div class="invalid-feedback">Field ini wajib diisi.</div>');
        //                 }
        //             } else {
        //                 $(this).removeClass('is-invalid');
        //                 $(this).next('.invalid-feedback').remove();
        //             }
        //         }
        //     });  

        //     if (!isValid) {
        //         Swal.fire({
        //             title: "Error!",
        //             text: "terdapat data yang kosong, tolong lengkapi.",
        //             type: 'error'
        //         });
        //         return;
        //     }
        //     let formData = $('#formReservasiEdit').serialize();
        //     formData += '&send_wa=0'; 
        //     Swal.fire({
        //     title: "Yakin Ingin Simpan?",
        //     text: "Data akan disimpan",
        //     type: 'warning',
        //     showConfirmButton:true,
        //     showCancelButton: true,
        //     confirmButtonText: 'Ya',
        //     cancelButtonText: 'Batal',
        //     focusConfirm:true
        //     }).then(result => {
        //         if (result.value) {
        //             // var form = $("#formReservasiEdit").serializeArray(); //array bila data mau dimanupulasi di frontend
        //             $.ajax({
        //                 dataType:'json',
        //                 url: "<?php echo base_url('Admision/updateReservasi')?>",
        //                 type: 'POST',
        //                 data: formData,
        //                 success: function(data){
        //                     if(data.status == 'success'){
        //                         Swal.fire({
        //                             title: "Data berhasil dirubah",
        //                             type: 'success',
        //                         }).then(function() {
        //                             location.reload();
        //                         }, 1000);
        //                     } else {
        //                         Swal.fire({
        //                             title: "Error!",
        //                             text: data.message, 
        //                             type: 'error'
        //                         });
        //                     }
        //                 },
        //                 error: function(jqXHR, textStatus, errorThrown) {
        //                     Swal.fire({
        //                         title: "Error!",
        //                         text: "Terjadi kesalahan dalam mengirim permintaan.",
        //                         type: 'error'
        //                     });
        //                 }
        //             });
        //         } else if(result.dismiss === Swal.DismissReason.cancel){
        //             Swal.fire("Data Tidak Tersimpan!", '', 'info');
        //         }
        //     });
        // });

        function validateForm() {
            let isValid = true;          
            $('#tgl_rencana_masuk, #cara_bayar, #kelasPasien, #dokter, #tujuan_dirawat, textarea[name="alasan_edit"]').each(function() {
                $(this).removeClass('is-invalid');
                if ($(this).hasClass('select2-hidden-accessible')) {
                    if (!$(this).val()) {
                        isValid = false;
                        $(this).next('.select2').find('.select2-selection').addClass('is-invalid');
                        if ($(this).next('.select2').next('.invalid-feedback').length === 0) {
                            $(this).next('.select2').after('<div class="invalid-feedback d-block">Field ini wajib dipilih.</div>');
                        }
                    } else {
                        $(this).next('.select2').find('.select2-selection').removeClass('is-invalid');
                        $(this).closest('.form-group').find('.invalid-feedback').remove();
                    }
                } else {
                    if ($(this).val().trim() === '') {
                        isValid = false;
                        $(this).addClass('is-invalid');
                        if ($(this).next('.invalid-feedback').length === 0) {
                            $(this).after('<div class="invalid-feedback">Field ini wajib diisi.</div>');
                        }
                    } else {
                        $(this).removeClass('is-invalid');
                        $(this).next('.invalid-feedback').remove();
                    }
                }
            });
            return isValid;
        }
        function submitForm(sendWA) {
            if (!validateForm()) {
                Swal.fire({
                    title: "Error!",
                    text: "terdapat data yang kosong, tolong lengkapi.",
                    type: 'error'
                });
                return;
            }

            let formData = $('#formReservasiEdit').serialize() + '&send_wa=' + sendWA;
            const reSimpel = $('input[name="RE_SIMPEL"]').val();
            if (reSimpel) {
                formData += '&RE_SIMPEL=' + reSimpel;
            }

            Swal.fire({
                title: "Yakin Ingin Simpan?",
                text: "Data akan disimpan",
                type: 'warning',
                showConfirmButton: true,
                showCancelButton: true,
                confirmButtonText: 'Ya',
                cancelButtonText: 'Batal',
                focusConfirm: true
            }).then(result => {
                if (result.value) {
                    $.ajax({
                        dataType: 'json',
                        url: "<?php echo base_url('Admision/updateReservasi')?>",
                        type: 'POST',
                        data: formData,
                        success: function(data) {
                            if(data.status == 'success') {
                                Swal.fire({
                                    title: "Data berhasil dirubah",
                                    type: 'success',
                                }).then(function() {
                                    location.reload();
                                }, 1000);
                            } else {
                                Swal.fire({
                                    title: "Error!",
                                    text: data.message,
                                    type: 'error'
                                });
                            }
                        },
                        error: function(jqXHR, textStatus, errorThrown) {
                            Swal.fire({
                                title: "Error!",
                                text: "Terjadi kesalahan dalam mengirim permintaan.",
                                type: 'error'
                            });
                        }
                    });
                } else if(result.dismiss === Swal.DismissReason.cancel) {
                    Swal.fire("Data Tidak Tersimpan!", '', 'info');
                }
            });
        }

        $('#updateKirim').on('click', function() {
            submitForm(1);
        });

        $('#updateRE').on('click', function() {
            submitForm(0);
        });
       
    });
</script>
