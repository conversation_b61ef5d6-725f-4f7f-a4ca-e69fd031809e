<form id="formPenjadwalan" method="post">
    <div class="card">
        <div class="card-header">
            <h5 class="card-title"><?= isset($detail['id_penjadwalan']) ? 'Ubah' : 'Buat' ?> Penjadwalan Operasi</h5>
        </div>
        <div class="card-body">
            <!-- Hidden Fields -->
            <input type="hidden" name="id_penjadwalan" value="<?= $detail['id_penjadwalan'] ?? null ?>">
            <input type="hidden" name="id_perjanjian" value="<?= $detail['id_perjanjian'] ?? null ?>">
            <input type="hidden" name="id_reservasi" value="<?= $detail['id_reservasi'] ?? null ?>">
            <input type="hidden" name="id_pendaftaran_operasi" id="idPendaftaranPenjadwalan" value="<?= $detail['id_tpo'] ?? null ?>">
            <input type="hidden" name="id_jk" id="jkPenjadwalan" value="<?= $detail['id_jk'] ?? null ?>">

            <div class="row">
                <!-- <PERSON><PERSON><PERSON> -->
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label for="mrPenjadwalan">Nomor RM</label>
                        <input type="text" name="norm" id="mrPenjadwalan" class="form-control" placeholder="Masukkan nomor RM" maxlength="6" value="<?= $detail['norm'] ?? null ?>" readonly>
                    </div>

                    <div class="form-group mb-3">
                        <label for="namaPenjadwalan">Nama</label>
                        <input type="text" name="nama" id="namaPenjadwalan" class="form-control" placeholder="Masukkan nama" value="<?= $detail['nama'] ?? null ?>" readonly>
                    </div>

                    <div class="form-group mb-3">
                        <label for="teleponPenjadwalan">Nomor telepon</label>
                        <input type="text" name="no_telp" id="teleponPenjadwalan" class="form-control" placeholder="Telepon pasien" value="<?= $detail['no_telp'] ?? null ?>" readonly>
                    </div>

                    <div class="form-group mb-3">
                        <label for="pendaftaran">Pendaftaran pra operasi</label>
                        <input type="text" name="pendaftaran" id="pendaftaran" class="form-control" value="<?= ($detail['id_tpo'] ?? '') . ($detail['nokun'] ? ' - ' . $detail['nokun'] : '') . ($detail['diagnosa_medis'] ? ' - ' . $detail['diagnosa_medis'] : '') . ($detail['tanggal_operasi'] ? ' - ' . date('d/m/Y', strtotime($detail['tanggal_operasi'])) : '') ?>" readonly>
                    </div>

                    <div class="form-group mb-3">
                        <label for="dokterPenjadwalan">Dokter</label>
                        <select name="dokter" id="dokterPenjadwalan" class="form-control">
                            <option value=""></option>
                            <?php foreach ($dokter as $d): ?>
                                <option id="dokterPenjadwalan<?= $d['id_dokter'] ?>" class="isiDokterPenjadwalan" value="<?= $d['id_dokter'] ?>" data-smf="<?= $d['id_smf'] ?>" <?= isset($detail['id_dokter']) && $detail['id_dokter'] == $d['id_dokter'] ? 'selected' : 'disabled' ?>>
                                    <?= $d['dokter'] . ' - ' . $d['smf'] ?>
                                </option>
                            <?php endforeach ?>
                        </select>
                    </div>

                    <div class="form-group mb-3">
                        <label for="diagnosisPenjadwalan">Diagnosis</label>
                        <textarea name="diagnosis" id="diagnosisPenjadwalan" class="form-control" placeholder="Tuliskan diagnosis" readonly><?= $detail['diagnosis'] ?? null ?></textarea>
                    </div>

                    <div class="form-group mb-3">
                        <label for="tindakanPenjadwalan">Tindakan</label>
                        <textarea name="tindakan" id="tindakanPenjadwalan" class="form-control" placeholder="Tuliskan tindakan operasi" readonly><?= $detail['tindakan'] ?? null ?></textarea>
                    </div>
                </div>

                <!-- Kolom Kanan -->
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label for="tglRawatPenjadwalan">Tanggal Rawat</label>
                        <div class="input-group">
                            <input type="date" name="tgl_rencanaMasuk" id="tglRawatPenjadwalan" class="form-control" placeholder="Pilih tanggal rawat" value="<?= $detail['tgl_rawat'] ?? null ?>" <?= isset($detail['tgl_rawat']) ? 'readonly' : null ?>>
                            <?php if (isset($detail['tgl_rawat']) && $detail['tgl_rawat'] != '-'): ?>
                            <div class="input-group-append">
                                <span class="input-group-text" id="iconCalendarRawat" style="cursor: pointer;">
                                    <i class="fa fa-calendar"></i>
                                </span>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="tanggalOperasiPenjadwalan">Tanggal operasi</label>
                        <div class="input-group">
                            <input type="date" name="tgl_operasi" id="tanggalOperasiPenjadwalan" class="form-control" placeholder="Tanggal operasi" value="<?= $detail['tgl_operasi'] ?? null ?>" readonly>
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary" type="button" id="pilihTanggalOperasiPenjadwalan">Pilih</button>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="kamarOperasiPenjadwalan">Kamar operasi</label>
                        <select name="kamar_operasi" id="kamarOperasiPenjadwalan" class="form-control">
                            <option value=""></option>
                            <?php foreach ($kamar_operasi as $ko): ?>
                                <option id="kamarOperasiPenjadwalan<?= $ko['id'] ?>" value="<?= $ko['id'] ?>" <?= isset($detail['kamar_operasi']) && $detail['kamar_operasi'] == $ko['id'] ? 'selected' : '' ?>><?= $ko['nama'] ?></option>
                            <?php endforeach ?>
                        </select>
                    </div>

                    <div class="form-group mb-3">
                        <label for="waktuOperasiPenjadwalan">Waktu operasi dimulai</label>
                        <input type="time" name="waktu_operasi" id="waktuOperasiPenjadwalan" class="form-control" placeholder="Waktu operasi" value="<?= $detail['waktu_operasi'] ?? null ?>" readonly>
                    </div>

                    <div class="form-group mb-3">
                        <label for="durasiOperasiPenjadwalan">Durasi operasi</label>
                        <div class="input-group">
                            <input type="number" name="durasi_operasi" id="durasiOperasiPenjadwalan" class="form-control" placeholder="Durasi operasi" value="<?= $detail['durasi_operasi'] ?? null ?>" readonly>
                            <div class="input-group-append">
                                <span class="input-group-text">menit</span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="dokterAnestesiPenjadwalan">Dokter anestesi</label>
                        <select name="dr_anestesi" id="dokterAnestesiPenjadwalan" class="form-control">
                            <option value=""></option>
                            <?php foreach ($dokter_anestesi as $da): ?>
                                <option id="dokterAnestesiPenjadwalan<?= $da['id_dokter'] ?>" value="<?= $da['id_dokter'] ?>" data-smf="<?= $da['id_smf'] ?>" <?= isset($detail['dr_anestesi']) && $detail['dr_anestesi'] == $da['id_dokter'] ? 'selected' : '' ?>>
                                    <?= $da['dokter'] . ' - ' . $da['smf'] ?>
                                </option>
                            <?php endforeach ?>
                        </select>
                    </div>

                    <div class="form-group mb-3">
                        <label for="jenisAnestesiPenjadwalan">Jenis anestesi</label>
                        <select name="jenis_anestesi" id="jenisAnestesiPenjadwalan" class="form-control">
                            <option value=""></option>
                            <?php foreach ($jenis_anestesi as $ja): ?>
                                <option id="jenisAnestesiPenjadwalan<?= $ja['id_variabel'] ?>" value="<?= $ja['id_variabel'] ?>" <?= isset($detail['jenis_anestesi']) && $detail['jenis_anestesi'] == $ja['id_variabel'] ? 'selected' : '' ?>>
                                    <?= $ja['variabel'] ?>
                                </option>
                            <?php endforeach ?>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
            <button type="submit" class="btn btn-primary">Simpan</button>
        </div>
    </div>
</form>

<script>
$(document).ready(function() {
    // Initialize Select2 for dropdowns
    $('#dokterAnestesiPenjadwalan').select2({
        placeholder: 'Pilih dokter anestesi',
        dropdownParent: $('#modalFormPenjadwalan')
    }).val(<?= $detail['dr_anestesi'] ?? 0 ?>).trigger('change');

    $('#jenisAnestesiPenjadwalan').select2({
        placeholder: 'Pilih jenis anestesi',
        dropdownParent: $('#modalFormPenjadwalan')
    }).val(<?= $detail['jenis_anestesi'] ?? 0 ?>).trigger('change');

    $('#dokterPenjadwalan').select2({
        placeholder: 'Pilih dokter',
        dropdownParent: $('#modalFormPenjadwalan')
    });

    $('#kamarOperasiPenjadwalan').select2({
        placeholder: 'Pilih kamar operasi',
        dropdownParent: $('#modalFormPenjadwalan')
    });

    // Calendar icon click handler for tanggal rawat
    $('#iconCalendarRawat').click(function() {
        if ($('#tglRawatPenjadwalan').val() !== '-') {
            $('#tglRawatPenjadwalan').datepicker({
                format: 'yyyy-mm-dd',
                autoclose: true,
                todayHighlight: true
            }).datepicker('show');
        }
    });

    // Datepicker change handler
    $('#tglRawatPenjadwalan').on('changeDate', function() {
        var namaPasien = $('#namaPenjadwalan').val();
        Swal.fire({
            title: 'Konfirmasi',
            text: 'Apakah anda yakin merubah tanggal dirawat/reservasi pasien ' + namaPasien + '?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Ya',
            cancelButtonText: 'Tidak'
        }).then((result) => {
            if (!result.isConfirmed) {
                // Reset to original value if cancelled
                $('#tglRawatPenjadwalan').val('<?= $detail['tgl_rawat'] ?? '' ?>');
            }
        });
    });

    // Pilih Tanggal Operasi button handler
    $('#pilihTanggalOperasiPenjadwalan').click(function() {
        $.ajax({
            url: '<?php echo base_url('PenjadwalanOperasi/modal_pilih_tanggal'); ?>',
            type: 'POST',
            success: function(response) {
                $('#modalPilihTanggalOperasiBody').html(response);
                $('#modalPilihTanggalOperasi').modal('show');
            },
            error: function() {
                Swal.fire('Error', 'Gagal memuat kalender', 'error');
            }
        });
    });

    // Form submit handler
    $('#formPenjadwalan').submit(function(e) {
        e.preventDefault();

        var formData = $(this).serialize();

        $.ajax({
            url: '<?php echo base_url('PenjadwalanOperasi/save_penjadwalan'); ?>',
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    Swal.fire('Berhasil', response.message, 'success').then(() => {
                        $('#modalFormPenjadwalan').modal('hide');
                        // Reload active table
                        if (typeof tableDaftarPerjanjian !== 'undefined' && tableDaftarPerjanjian) {
                            tableDaftarPerjanjian.ajax.reload();
                        }
                        if (typeof tableOperasiHariIni !== 'undefined' && tableOperasiHariIni) {
                            tableOperasiHariIni.ajax.reload();
                        }
                    });
                } else {
                    Swal.fire('Error', response.message, 'error');
                }
            },
            error: function() {
                Swal.fire('Error', 'Gagal menyimpan data', 'error');
            }
        });
    });
});
</script>
