bantu saya merubah tampilan filter tanggal dan <PERSON><PERSON><PERSON>,senin dll ke sebelumnya dan aturannya begini saat milih tanggal baru kan aktif day-filter nya sesuai tanggal dan default semua hari, dan bila mau klik hanya hari yang aktif seperti sebelumnya bentuknya jangan button kotak tetapi seperti sebelumnya dan datatable tidak perlu ada kotak seperti pemisah datatable(garis yang melilinginya dihilangin). Dan saya ingin merubah kembali jadi Index.php jangan IndexNew.php serta perhatikan case sensitive besar kecilnya huruf karena saya pakai server linux
tambahkan beberapa fitur:
1.	table-daftar-perjanjian dan table-operasi-hari-ini seharusnya ada Aksi isinya icon edit warna biru dan icon X warna merah. Edit akan membuka form  dan bil icon X maka akan keluar swallfire Apakah anda yakin akan menghapus  ya tidak 
2.	form isinya bisa Buat atau Update tergantung dia ada jadwal atau tidak. 
Tampilannya modal seperti gambar dengancatatan selectoption class="form-control" agar panjangnya sama semua dan responsift. Bila Update maka judulnya Update Penjadwalan Operasi.  Jadi kirim ID isinya id_penjadwalan  atau id_tpo'. bila id_penjadwalan ada maka Update dan bila 
id_penjadwalan  tidak ada dan id_tpo' nya ada maka Buat .gunakan inisial buat dan edit. 
 
$dataForm = $this-> ModelPenjadwalanOperasi->isiForm($jenis, $ID);
Querynya :
isiForm:
 $this->db->select([
            'ppo.id AS id_penjadwalan',
            'rmp.id AS id_perjanjian',
            'rr.id AS id_reservasi',
            'wlo.id AS id_waiting_list_operasi',
            'tpo.id AS id_tpo',
            'mr.JENIS_KELAMIN AS id_jk',
            'mr.NORM AS norm',
            'master.getNamaLengkap(mr.NORM) AS nama',
            'kp.NOMOR AS no_telp',
            'wlo.id_dokter',
            'tpo.diagnosa_medis AS diagnosis',
            'tpo.rencana_tindakan_operasi AS tindakan',
            'IF(rr.tgl_rencanaMasuk IS NULL, '-', rr.tgl_rencanaMasuk) AS tgl_rawat,
            'IF(ppo.tgl_operasi IS NULL, rmp.TANGGAL, ppo.tgl_operasi) AS tgl_operasi',
            'ppo.kamar_operasi',
            // 'LEFT(ppo.waktu_operasi, 5) AS waktu_operasi',
            'TIME_FORMAT(ppo.waktu_operasi, "%H:%i") AS waktu_operasi',
            'ppo.durasi_operasi',
            'ppo.dr_anestesi',
            'IF(ppo.jenis_anestesi IS NULL, tpo.rencana_jenis_pembiusan, ppo.jenis_anestesi) AS jenis_anestesi',
            'tpo.rencana_jenis_pembiusan_lain',
            'ppo.menunggu_konfirmasi_ruang',
            'ppo.ruang_rawat',
            'tpo.nokun',
            'tpo.diagnosa_medis',
            'tpo.tanggal_operasi',
            'tpo.rencana_jenis_pembiusan',
            'tpo.perkiraan_lama_operasi',
            'tpo.potong_beku',
            'tpo.join_operasi',
            'tpo.sifat_operasi',
            ‘IF(ppo.tujuan_rs IS NULL, 16, ppo.tujuan_rs) AS tujuan_rs’        ]);
        
        $this->db->from('medis.tb_waiting_list_operasi wlo');
        $this->db->join('perjanjian.penjadwalan_operasi ppo', 'ppo.id_waiting_list_operasi = wlo.id', 'left');
        $this->db->join('remun_medis.perjanjian rmp', 'rmp.ID_WAITING_LIST_OPERASI = wlo.id', 'left');
        $this->db->join('db_reservasi.tb_reservasi rr', 'rr.id_perjanjian = rmp.ID', 'left');
        $this->db->join('medis.tb_pendaftaran_operasi tpo', 'tpo.id = wlo.id_pendaftaran_operasi', 'left');
        $this->db->join('master.pasien mr', 'mr.NORM = wlo.norm', 'left');
        $this->db->join('master.kontak_pasien kp', 'kp.NORM = mr.NORM AND kp.JENIS = 3', 'left');

        if ($jenis == buat) {
            $this->db->where('ppo.id', $ID);
        } elseif ($jenis == edit) {
            $this->db->where('tpo.id', $ID);
        }

        $query = $this->db->get();
        return $query->row_array();

#dokter query:  public function dokter()
    {
        $this->db->select(
            'd.ID id_dokter, master.getNamaLengkapPegawai(d.NIP) dokter, p.SMF id_smf, smf.DESKRIPSI smf'
        );
        $this->db->from($this->_table);
        $this->db->join('master.pegawai p', 'p.NIP = d.NIP', 'left');
        $this->db->join('master.referensi smf', 'smf.ID = p.SMF AND smf.JENIS = 26', 'left');
        $this->db->where('d.STATUS', 1);
        $this->db->order_by('p.NAMA', 'asc');
        $query = $this->db->get();
        return $query->result_array();
    }

#'kamar_operasi' => $this-> ModelPenjadwalanOperasi ->kamar(105090104),
querynya :
  function ambil($id_ruang)
    {
        $this->db->select('id, nama, status');
        $this->db->from('db_master.tb_kamar');
        $this->db->where('id_ruang', $id_ruang);
        $this->db->where('status', 1);
        $this->db->order_by('nama', 'asc');
        $query = $this->db->get();
        return $query->result_array();
    }

#dokter anastesi query :  public function dokter_anestesi()
    {
        $this->db->select(
            'd.ID id_dokter, master.getNamaLengkapPegawai(d.NIP) dokter, p.SMF id_smf, smf.DESKRIPSI smf'
        );
        $this->db->from('master.dokter d');
        $this->db->join('master.pegawai p', 'p.NIP = d.NIP', 'left');
        $this->db->join('master.referensi smf', 'smf.ID = p.SMF AND smf.JENIS = 26', 'left');
        $this->db->where_in('smf.ID', [6, 46, 55]);
        $this->db->where('d.STATUS', 1);
        $this->db->order_by('p.NAMA', 'asc');
        $query = $this->db->get();
        return $query->result_array();
    }

#'jenis_anestesi' => $this-> ModelPenjadwalanOperasi
->referensi(622),
Querynya :
 public function referensi($id)
    {
        $this->db->select('id_variabel, variabel, nilai, status, status_checked');
        $this->db->from('db_master.variabel');
        $this->db->where('id_referensi', $id);
        $this->db->order_by('id_variabel', 'asc');
        $this->db->order_by('seq', 'asc');
        $this->db->where('status !=', 0);
        $query = $this->db->get();
        return $query->result_array();
    }





 //////
Aturan isinya modal :
1.	Desain formnya  isinya  asal datanya:
<h5 class="card-title"><?= isset($detail['id_penjadwalan']) ? 'Ubah' : 'Buat' ?> Penjadwalan Operasi</h5>
 <input type="hidden" name="id_penjadwalan" value="<?= $detail['id_penjadwalan'] ?? null ?>">
            <input type="hidden" name="id_perjanjian" value="<?= $detail['id_perjanjian'] ?? null ?>">
            <input type="hidden" name="id_reservasi" value="<?= $detail['id_reservasi'] ?? null ?>">
            <input type="hidden" name="id_pendaftaran_operasi" id="idPendaftaranPenjadwalan" value="<?= $detail['id_tpo'] ?? null ?>">
            <input type="hidden" name="id_jk" id="jkPenjadwalan" value="<?= $detail['id_jk'] ?? null ?>">
bagian inputan 
Nomor Rm : <input type="text" name="norm" id="mrPenjadwalan" class="form-control" placeholder="Masukkan nomor RM" maxlength="6" value="<?= $detail['norm'] ?? null ?>" readonly>
Nama : <input type="text" name="nama" id="namaPenjadwalan" class="form-control" placeholder="Masukkan nama" value="<?= $detail['nama'] ?? null ?>" readonly>
Nomor telepon : <input type="text" name="no_telp" id="teleponPenjadwalan" class="form-control form-control-sm" placeholder="Telepon pasien" value="<?= $detail['no_telp'] ?? null ?>" readonly>
Pendaftaran pra operasi:'<input type="text" name="pendaftaran" id="pendaftaran" value="<?= $detail['id_tpo'] ?>'><?= $detail['nokun'] . ' - ' . $detail['diagnosa_medis'] . ' - ' . date('d/m/Y', strtotime($detail['tanggal_operasi'])) ?>
Dokter :  <select name="dokter" id="dokterPenjadwalan" class="form-select single-select">
                                <option value=""></option>
                                <?php foreach ($dokter as $d): ?>
                                    <option id="dokterPenjadwalan<?= $d['id_dokter'] ?>" class="isiDokterPenjadwalan" value="<?= $d['id_dokter'] ?>" data-smf="<?= $d['id_smf'] ?>" <?= isset($detail['id_dokter']) && $detail['id_dokter'] == $d['id_dokter'] ? 'selected' : 'disabled' ?>>
                                        <?= $d['dokter'] . ' - ' . $d['smf'] ?>
                                    </option>
                                <?php endforeach ?>
                            </select>

diagnosa : <textarea name="diagnosis" id="diagnosisPenjadwalan" class="form-control form-control-sm" placeholder="Tuliskan diagnosis" readonly><?= $detail['diagnosis'] ?? null ?></textarea>
tindakan : <textarea name="tindakan" id="tindakanPenjadwalan" class="form-control form-control-sm" placeholder="Tuliskan tindakan operasi" readonly><?= $detail['tindakan'] ?? null ?></textarea>
Tanggal Rawat : <input type="date" name="tgl_rencanaMasuk" id="tglRawatPenjadwalan" class="form-control form-control-sm" placeholder="Pilih tanggal rawat" value="<?= $detail['tgl_rawat'] ?? null ?>" <?= isset($detail['tgl_rawat']) ? 'readonly' : null ?>>
Tanggal operasi :   <input type="date" name="tgl_operasi" id="tanggalOperasiPenjadwalan" class="form-control form-control-sm" placeholder="Tanggal operasi" value="<?= $detail['tgl_operasi'] ?? null ?>" readonly>
                                <button class="btn btn-outline-secondary" type="button" id="pilihTanggalOperasiPenjadwalan">Pilih</button>

Kamar operasi :  <select name="kamar_operasi" id="kamarOperasiPenjadwalan" class="form-select single-select">
                                <option value=""></option>
                                <?php foreach ($kamar_operasi as $ko): ?>
                                    <option id="kamarOperasiPenjadwalan<?= $ko['id'] ?>" value="<?= $ko['id'] ?>"><?= $ko['nama'] ?></option>
                                <?php endforeach ?>
                            </select>
Waktu operasi dimulai : <input type="time" name="waktu_operasi" id="waktuOperasiPenjadwalan" class="form-control form-control-sm" placeholder="Waktu operasi" value="<?= $detail['waktu_operasi'] ?? null ?>" readonly>
Durasi operasi : <input type="number" name="durasi_operasi" id="durasiOperasiPenjadwalan" class="form-control form-control-sm" placeholder="Durasi operasi" value="<?= $detail['durasi_operasi'] ?? null ?>" readonly>
Dokter anastesi : <select name="dr_anestesi" id="dokterAnestesiPenjadwalan" class="form-select single-select">
                                <option value=""></option>
                                <?php foreach ($dokter_anestesi as $da): ?>
                                    <option id="dokterAnestesiPenjadwalan<?= $da['id_dokter'] ?>" value="<?= $da['id_dokter'] ?>" data-smf="<?= $da['id_smf'] ?>">
                                        <?= $da['dokter'] . ' - ' . $da['smf'] ?>
                                    </option>
                                <?php endforeach ?>
                            </select>
javascript : $('#dokterAnestesiPenjadwalan').select2({
            placeholder: 'Pilih dokter anestesi'
        }).val(<?= $detail['dr_anestesi'] ?? 0 ?>).trigger('change');
Jenis anestesi: <select name="jenis_anestesi" id="jenisAnestesiPenjadwalan" class="form-select single-select">
                                <option value=""></option>
                                <?php foreach ($jenis_anestesi as $ja): ?>
                                    <option id="jenisAnestesiPenjadwalan<?= $ja['id_variabel'] ?>" value="<?= $ja['id_variabel'] ?>">
                                        <?= $ja['variabel'] ?>
                                    </option>
                                <?php endforeach ?>
                            </select>
javascript : $('#jenisAnestesiPenjadwalan').select2({
            placeholder: 'Pilih jenis anestesi'
        }).val(<?= $detail['jenis_anestesi'] ?? 0 ?>).trigger('change');

2.	tgl_rencanaMasuk  di inputannya berikan span icon kalender dipojok kanan, bila isinya bukan “–“ maka bisa di klik  keluar kalender dengan menggunakan plugin datepicker desain yang bagus bila setelah dipilih maka keluar swalfire apakah anda yakin merubah tanggal dirawat/ reservasi pasien nama_pasien.
3.	pilihTanggalOperasiPenjadwalan akan tampil  
dimana rubah tampilannya agar lebih sesuai dengan teamplate saya warnanya putih dengan header warnanya biru, slot 1- 4 OKA 1 dan 5-8 OKA 2 kamar berdasarkan c.kamar_operasi. Ambil dari queryjadwal. Jadi tampilannya harus merepresentasikan ketersediaan slot operasi per hari, dengan detail dan logika pengisian sebagai berikut:

Struktur dan Konten Kotak Jadwal
Setiap kotak pada tampilan akan merepresentasikan satu slot operasi dan berisi informasi yang diambil dari data sebagai berikut:

Pasien: a.NAMAPASIEN [a.NOMR]

Dokter Bedah: db_rekammedis.getNamaLengkapDokter(a.dokter_bedah) AS nama_dokter

Diagnosa: a.diagnosa_medis

Rencana Tindakan: a.rencana_tindakan_operasi

Waktu: c. waktu_operasi - waktu_selesai
 

Queryjadwal:
SELECT
    c.status AS status_jadwal,
    c.id AS id_penjadwalan,
    b.ID AS perjanjian,
    c.tgl_operasi AS tanggal_operasi,
    c.waktu_operasi,
    c.slot_operasi,
    c.kamar_operasi,
    CONCAT(b.NAMAPASIEN, ' [', b.NOMR, ']') AS pasien_info,
    db_rekammedis.getNamaLengkapDokter(a.dokter_bedah) AS nama_dokter,
    a.rencana_tindakan_operasi,
    CONCAT(
        TIME_FORMAT(c.waktu_operasi, '%H:%i'),
        ' - ',
        DATE_FORMAT(
            ADDTIME(
                STR_TO_DATE(c.waktu_operasi, '%H:%i'),
                SEC_TO_TIME(c.durasi_operasi * 60)
            ),
            '%H:%i'
        )
    ) AS waktu_selesai,
    DATE_FORMAT(
        ADDTIME(
            STR_TO_DATE(c.waktu_operasi, '%H:%i'),
            SEC_TO_TIME((c.durasi_operasi + 30) * 60)
        ),
        '%H:%i'
    ) AS waktu_selesai_plus_interval,
    TIME_FORMAT(c.waktu_operasi, '%H:%i') AS jam_mulai_sort
FROM perjanjian.penjadwalan_operasi c
LEFT JOIN remun_medis.perjanjian b
    ON c.id_perjanjian = b.ID
    AND b.ID_RUANGAN = '105090104'
LEFT JOIN medis.tb_pendaftaran_operasi a
    ON b.ID_PENDAFTARAN_OPERASI = a.id
WHERE
    b.ID_RUANGAN = '105090104'
    AND a.status != 0
    AND b.STATUS != 0
    AND c.tgl_operasi BETWEEN ? AND ? (ambil 30 hari )
            ORDER BY c.tgl_operasi ASC, c.waktu_operasi ASC

# untuk 

 # gunakan serverside true untuk load data untuk Queryjadwal 