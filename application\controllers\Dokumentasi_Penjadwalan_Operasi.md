# Penjadwalan Operasi PERBAIKAN
1. untuk filter tanggal dirubah bentuknya Periode tanggal operasi Dari tanggal inputan tanggal  spasi sampai tanggal inputan tanggal. hilangkan btn-filter-daftar dan btn-reset-daftar jadi gunakan on change di tanggal nya(tanggal mulai dan tanggal akhir) untuk menvalidasi day-filter-daftar. untuk day-filter-daftar jangan berbentuk button. untuk fungsinya pelajari yang sudah ada baik dari desain css, fungsi setiap buttonnya yang diganti dengan change.
2. Modal Form Penjadwalan Operasi penempatannya langsung misah saja letakan header dan footer di FormPenjadwalan.php agar lebih responsift dan rapih headernya. misal meletakan <div class="viewmodal"></div>  untk menampung konten formnya di Index.php
3. untuk inputan text,select option buat responsift dan ukurannya form-control untuk select nya witdh 100 % agar rapih sesuaikan agar sangat rapih tulisanya. dan modalnya ukuran lebih diperbesar dan bantu saya buat jadi 3 kolom lalu kebawah. untuk inputan yang tidak readonly letakan paling bawah untuk inputannya. tambahkan selectoption inputan berupa <div class="form-group">
                            <label for="cara_bayar">Cara Bayar</label>
                            <input type="text" class="form-control validate-input" name="cara_bayar" id="cara_bayar" value="<?= $BAYAR; ?>">
                            <input type="hidden" name="idCara_bayar" id="idCara_bayar" value="<?= $ID_BAYAR; ?>">
                        </div> dan <div class="form-group">
                            <label for="kelasPasien">Kelas</label>
                            <select class="form-control validate-input" name="kelasPasien" id="kelasPasien">
                            <?php foreach ($list_kelas as $kelas): ?>
                                <option value="<?= ($kelas['ID']); ?>" 
                                    <?= isset($KELAS) && $KELAS == $kelas['ID'] ? 'selected' : ''; ?>>
                                    <?= ($kelas['DESKRIPSI']); ?>
                                </option>
                            <?php endforeach; ?>
                            </select>
                        </div> seperti di @c:\xampp\htdocs\rekam_medis/application\views\Admision\reservasi\IndexReservasiEdit.php  prosesnya sama tapi ambil data selectednya dari 'rr.id_cara_bayar'dan 'rr.id_kelas' dengan catatan 2 tambahan ini ada kalau id_reservasi ada .untuk buttonnya simpan ganti konfirm
4. perbaiki btn btn-sm btn-primary btn-edit seharusnya id yang diambil sesuai kondisi bila id_penjadwalan ada maka ubah dan lempar id_penjadwalan tsb.dan bila tidak ada ambil id_tpo dan jadi buat.
5. table-daftar-perjanjian isinya berikan warna  bayground kuning atau hijau dengan status kuning artinya belum memiliki penjadwalan operasi dan hijau artinya sudah memiliki penjadwalan operasi memiliki id_penjadwalan.
6. untuk isi  $this->load->view('PenjadwalanOperasi/ModalPilihTanggal'); tolong perbaiki sekali load itu data 7 hari sesuaikan pada bulan misal masuknya tanggalnya ada agustus-september maka judulnya itu. dan data yang sudah masuk penjadwalan kenapa tidak tampil, untuk   if ($kamar == '105090101') { // OKA 1
                $slot_key = 'oka1_slot' . $slot;
            } else { // OKA 2
                $slot_key = 'oka2_slot' . ($slot - 4);
            } buat dinamis karena bisa jadi ada penambahan . baca aja c.slot_operasi untuk per id satu harinya hanya punya 4 slot. atau bisa join ke db_master.tb_kamar ambil z z.id id_kamar on z.id_ruang=105090104 dan status = 1. usahakan datanya muncul dulu 
7. iconCalendarRawat saat di klik harusnya keluarkan dulu swalfire apakah anada yakin merubah tanggal rawat/reservasi pasien nama pasien, bila ya maka keluar kalender dan bila tidak ya tetap di modal. dan berikan jdn link plugin datepicker yang bagus.
8. inputan waktuOperasiPenjadwalan kanannya berikan icon jam bila di klik maka keluar swalfire apakah anada yakin merubah waktu operasi pasien nama pasien, bila ya maka keluar inputan waktu dan bila tidak ya tetap di modal. dan berikan jdn link plugin timepicker yang bentuknya jam tetapi di set 24 jam jangan gunakan pm am.
9. id_reservasi bila ada ini kan ada inputannya idCara_bayar dan kelasPasien maka update db_reservasi.tb_reservasi 
