<!-- Start Page Title -->
<div class="row">
    <div class="col-12">
        <div class="page-title-box">
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="javascript: void(0);">Operasi</a></li>
                    <li class="breadcrumb-item active">Penjadwalan Operasi</li>
                </ol>
            </div>
            <h4 class="page-title">Penjadwalan Operasi</h4>
        </div>
    </div>
</div>
<!-- End Page Title -->

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <!-- Tab Navigation -->
                <ul class="nav nav-tabs nav-bordered" id="operasiTabs" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="daftar-perjanjian-tab" data-toggle="tab" href="#daftar-perjanjian" role="tab" aria-controls="daftar-perjanjian" aria-selected="true">
                            <i class="mdi mdi-calendar-clock"></i> Daftar Perjanjian Operasi
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="operasi-hari-ini-tab" data-toggle="tab" href="#operasi-hari-ini" role="tab" aria-controls="operasi-hari-ini" aria-selected="false">
                            <i class="mdi mdi-calendar-today"></i> Operasi Hari Ini
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="history-operasi-tab" data-toggle="tab" href="#history-operasi" role="tab" aria-controls="history-operasi" aria-selected="false">
                            <i class="mdi mdi-history"></i> History/Selesai Operasi
                        </a>
                    </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content" id="operasiTabsContent">
                    <!-- Tab Daftar Perjanjian Operasi -->
                    <div class="tab-pane fade show active" id="daftar-perjanjian" role="tabpanel" aria-labelledby="daftar-perjanjian-tab">
                        <!-- Filter Section -->
                        <div class="row mb-3 mt-3">
                            <div class="col-md-3">
                                <label for="tanggal_mulai_daftar">Tanggal Mulai:</label>
                                <input type="date" class="form-control" id="tanggal_mulai_daftar" name="tanggal_mulai_daftar">
                            </div>
                            <div class="col-md-3">
                                <label for="tanggal_akhir_daftar">Tanggal Akhir:</label>
                                <input type="date" class="form-control" id="tanggal_akhir_daftar" name="tanggal_akhir_daftar">
                            </div>
                            <div class="col-md-3">
                                <label>&nbsp;</label><br>
                                <button type="button" class="btn btn-primary" id="btn-filter-daftar">
                                    <i class="fa fa-search"></i> Filter
                                </button>
                                <button type="button" class="btn btn-secondary" id="btn-reset-daftar">
                                    <i class="fa fa-refresh"></i> Reset
                                </button>
                            </div>
                        </div>

                        <!-- Day Filter -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <div id="day-filter-daftar">
                                    <a href="#" class="btn-day active" data-day="All">Semua Hari</a>
                                    <a href="#" class="btn-day" data-day="Monday">Senin</a>
                                    <a href="#" class="btn-day" data-day="Tuesday">Selasa</a>
                                    <a href="#" class="btn-day" data-day="Wednesday">Rabu</a>
                                    <a href="#" class="btn-day" data-day="Thursday">Kamis</a>
                                    <a href="#" class="btn-day" data-day="Friday">Jumat</a>
                                </div>
                            </div>
                        </div>

                        <!-- DataTable -->
                        <table id="table-daftar-perjanjian" class="table table-striped dt-responsive nowrap" style="width: 100%;">
                            <thead>
                                <tr>
                                    <th width="4%">No</th>
                                    <th width="8%">Tgl Operasi</th>
                                    <th width="10%">Kamar Operasi</th>
                                    <th width="8%">Tgl Dibuat</th>
                                    <th width="12%">Nama Pasien</th>
                                    <th width="8%">NORM</th>
                                    <th width="12%">Tgl Lahir (Umur)</th>
                                    <th width="10%">Ruang Rawat</th>
                                    <th width="10%">Diagnosis</th>
                                    <th width="10%">Tindakan</th>
                                    <th width="8%">Operator</th>
                                    <th width="8%">Anestesi</th>
                                    <th width="8%">Catatan</th>
                                    <th width="8%">Tujuan RS</th>
                                    <th width="8%">Aksi</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>

                    <!-- Tab Operasi Hari Ini -->
                    <div class="tab-pane fade" id="operasi-hari-ini" role="tabpanel" aria-labelledby="operasi-hari-ini-tab">
                        <!-- Tanggal Hari Ini -->
                        <div class="text-center mt-3 mb-4">
                            <div id="tanggal-hari-ini" class="alert alert-info" style="font-size: 1.25rem; font-weight: 600;">
                                <i class="mdi mdi-calendar-today mr-2"></i>
                                <span id="tanggal-display"></span>
                            </div>
                        </div>

                        <!-- DataTable -->
                        <table id="table-operasi-hari-ini" class="table table-striped dt-responsive nowrap" style="width: 100%;">
                            <thead>
                                <tr>
                                    <th width="4%">No</th>
                                    <th width="12%">Kamar Operasi</th>
                                    <th width="10%">Tgl Dibuat</th>
                                    <th width="15%">Nama Pasien</th>
                                    <th width="8%">NORM</th>
                                    <th width="12%">Tgl Lahir (Umur)</th>
                                    <th width="10%">Ruang Rawat</th>
                                    <th width="10%">Diagnosis</th>
                                    <th width="10%">Tindakan</th>
                                    <th width="8%">Operator</th>
                                    <th width="8%">Anestesi</th>
                                    <th width="8%">Catatan</th>
                                    <th width="8%">Ruang</th>
                                    <th width="8%">Tujuan RS</th>
                                    <th width="8%">Aksi</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>

                    <!-- Tab History/Selesai Operasi -->
                    <div class="tab-pane fade" id="history-operasi" role="tabpanel" aria-labelledby="history-operasi-tab">
                        <!-- Filter Section -->
                        <div class="row mb-3 mt-3">
                            <div class="col-md-3">
                                <label for="tanggal_mulai_history">Tanggal Mulai:</label>
                                <input type="date" class="form-control" id="tanggal_mulai_history" name="tanggal_mulai_history">
                            </div>
                            <div class="col-md-3">
                                <label for="tanggal_akhir_history">Tanggal Akhir:</label>
                                <input type="date" class="form-control" id="tanggal_akhir_history" name="tanggal_akhir_history">
                            </div>
                            <div class="col-md-3">
                                <label>&nbsp;</label><br>
                                <button type="button" class="btn btn-primary" id="btn-filter-history">
                                    <i class="fa fa-search"></i> Filter
                                </button>
                                <button type="button" class="btn btn-secondary" id="btn-reset-history">
                                    <i class="fa fa-refresh"></i> Reset
                                </button>
                            </div>
                        </div>

                        <!-- Day Filter -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <div id="day-filter-history">
                                    <a href="#" class="btn-day active" data-day="All">Semua Hari</a>
                                    <a href="#" class="btn-day" data-day="Monday">Senin</a>
                                    <a href="#" class="btn-day" data-day="Tuesday">Selasa</a>
                                    <a href="#" class="btn-day" data-day="Wednesday">Rabu</a>
                                    <a href="#" class="btn-day" data-day="Thursday">Kamis</a>
                                    <a href="#" class="btn-day" data-day="Friday">Jumat</a>
                                </div>
                            </div>
                        </div>

                        <!-- DataTable -->
                        <table id="table-history-operasi" class="table table-striped dt-responsive nowrap" style="width: 100%;">
                            <thead>
                                <tr>
                                    <th width="4%">No</th>
                                    <th width="8%">Tanggal Tindakan</th>
                                    <th width="10%">Kamar Operasi</th>
                                    <th width="15%">Nama Pasien (NORM)</th>
                                    <th width="12%">Tanggal Lahir (Umur)</th>
                                    <th width="10%">Ruang Kelas/Rawat</th>
                                    <th width="12%">Diagnosis</th>
                                    <th width="12%">Tindakan</th>
                                    <th width="8%">Operator</th>
                                    <th width="8%">Dokter Anestesi</th>
                                    <th width="8%">Status</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Form Penjadwalan Operasi -->
<div class="modal fade" id="modalFormPenjadwalan" tabindex="-1" role="dialog" aria-labelledby="modalFormPenjadwalanLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modalFormPenjadwalanBody">
                <!-- Form content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Modal Pilih Tanggal Operasi -->
<div class="modal fade" id="modalPilihTanggalOperasi" tabindex="-1" role="dialog" aria-labelledby="modalPilihTanggalOperasiLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="modalPilihTanggalOperasiLabel">
                    <i class="mdi mdi-calendar-clock"></i> List Perjanjian Operasi
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modalPilihTanggalOperasiBody">
                <!-- Calendar content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<style>
/* Day filter buttons - style seperti sebelumnya */
.btn-day {
    display: inline-block;
    padding: 6px 12px;
    margin: 2px;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.42857143;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid #ddd;
    border-radius: 4px;
    color: #333;
    background-color: #fff;
    text-decoration: none;
    transition: all 0.2s ease-in-out;
}

.btn-day:hover {
    color: #333;
    background-color: #e6e6e6;
    border-color: #adadad;
    text-decoration: none;
}

.btn-day.active {
    color: #fff;
    background-color: #337ab7;
    border-color: #2e6da4;
}

.btn-day.disabled {
    opacity: 0.4;
    cursor: not-allowed;
    pointer-events: none;
}

/* Remove table borders */
.table {
    border: none !important;
}

.table th,
.table td {
    border: 1px solid #dee2e6;
}

.table thead th {
    border-bottom: 2px solid #dee2e6;
}

/* Tab content spacing */
.tab-content {
    padding-top: 1.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .btn-day {
        font-size: 12px;
        padding: 4px 8px;
        margin: 1px;
    }
}
</style>

<script>
$(document).ready(function() {
    var tableDaftarPerjanjian, tableOperasiHariIni, tableHistoryOperasi;
    var currentTab = 'daftar-perjanjian';

    // Load active tab from localStorage
    var savedTab = localStorage.getItem('penjadwalan_operasi_active_tab');
    if (savedTab) {
        currentTab = savedTab;
        $('.nav-link').removeClass('active');
        $('.tab-pane').removeClass('show active');
        $('#' + currentTab + '-tab').addClass('active');
        $('#' + currentTab).addClass('show active');
    }

    // Set tanggal hari ini
    var today = new Date();
    var options = {
        day: '2-digit',
        month: 'long',
        year: 'numeric',
        locale: 'id-ID'
    };
    var formattedDate = today.toLocaleDateString('id-ID', options).toUpperCase();
    $('#tanggal-display').text(formattedDate);

    // Initialize tables based on active tab
    if (currentTab === 'daftar-perjanjian') {
        initDaftarPerjanjianTable();
    } else if (currentTab === 'operasi-hari-ini') {
        initOperasiHariIniTable();
    } else if (currentTab === 'history-operasi') {
        initHistoryOperasiTable();
    }

    // Tab click handlers
    $('.nav-link').on('click', function() {
        var tabId = $(this).attr('href').substring(1);
        currentTab = tabId;
        localStorage.setItem('penjadwalan_operasi_active_tab', tabId);

        // Initialize table for the clicked tab
        if (tabId === 'daftar-perjanjian' && !tableDaftarPerjanjian) {
            initDaftarPerjanjianTable();
        } else if (tabId === 'operasi-hari-ini' && !tableOperasiHariIni) {
            initOperasiHariIniTable();
        } else if (tabId === 'history-operasi' && !tableHistoryOperasi) {
            initHistoryOperasiTable();
        }
    });

    // Initialize Daftar Perjanjian Table
    function initDaftarPerjanjianTable() {
        tableDaftarPerjanjian = $('#table-daftar-perjanjian').DataTable({
            processing: true,
            serverSide: true,
            ajax: {
                url: '<?php echo base_url('PenjadwalanOperasi/get_daftar_perjanjian'); ?>',
                type: 'POST',
                data: function(d) {
                    d.tanggal_mulai = $('#tanggal_mulai_daftar').val();
                    d.tanggal_akhir = $('#tanggal_akhir_daftar').val();
                    d.hari = $('#day-filter-daftar .btn-day.active').data('day');
                }
            },
            columns: [
                { data: 0, orderable: false, searchable: false },
                { data: 1, orderable: true },
                { data: 2, orderable: true },
                { data: 3, orderable: true },
                { data: 4, orderable: true },
                { data: 5, orderable: false },
                { data: 6, orderable: false },
                { data: 7, orderable: true },
                { data: 8, orderable: true },
                { data: 9, orderable: true },
                { data: 10, orderable: true },
                { data: 11, orderable: true },
                { data: 12, orderable: false },
                { data: 13, orderable: false },
                { data: 14, orderable: false, searchable: false }
            ],
            order: [[1, 'desc']],
            pageLength: 25,
            responsive: true,
            language: {
                processing: '<div class="loading-spinner"></div> Memuat data...',
                loadingRecords: '<div class="loading-spinner"></div> Memuat...',
                lengthMenu: "Tampilkan _MENU_ data per halaman",
                zeroRecords: "Data tidak ditemukan",
                info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
                infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
                infoFiltered: "(difilter dari _MAX_ total data)",
                search: "Cari:",
                paginate: {
                    first: "Pertama",
                    last: "Terakhir",
                    next: "Selanjutnya",
                    previous: "Sebelumnya"
                }
            }
        });
    }

    // Initialize Operasi Hari Ini Table
    function initOperasiHariIniTable() {
        tableOperasiHariIni = $('#table-operasi-hari-ini').DataTable({
            processing: true,
            serverSide: true,
            ajax: {
                url: '<?php echo base_url('PenjadwalanOperasi/get_operasi_hari_ini'); ?>',
                type: 'POST'
            },
            columns: [
                { data: 0, orderable: false, searchable: false },
                { data: 1, orderable: true },
                { data: 2, orderable: true },
                { data: 3, orderable: true },
                { data: 4, orderable: false },
                { data: 5, orderable: false },
                { data: 6, orderable: true },
                { data: 7, orderable: true },
                { data: 8, orderable: true },
                { data: 9, orderable: true },
                { data: 10, orderable: true },
                { data: 11, orderable: false },
                { data: 12, orderable: false },
                { data: 13, orderable: false },
                { data: 14, orderable: false, searchable: false }
            ],
            order: [[1, 'asc']],
            pageLength: 25,
            responsive: true,
            language: {
                processing: '<div class="loading-spinner"></div> Memuat data...',
                loadingRecords: '<div class="loading-spinner"></div> Memuat...',
                lengthMenu: "Tampilkan _MENU_ data per halaman",
                zeroRecords: "Data tidak ditemukan",
                info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
                infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
                infoFiltered: "(difilter dari _MAX_ total data)",
                search: "Cari:",
                paginate: {
                    first: "Pertama",
                    last: "Terakhir",
                    next: "Selanjutnya",
                    previous: "Sebelumnya"
                }
            }
        });
    }

    // Initialize History Operasi Table
    function initHistoryOperasiTable() {
        tableHistoryOperasi = $('#table-history-operasi').DataTable({
            processing: true,
            serverSide: true,
            ajax: {
                url: '<?php echo base_url('PenjadwalanOperasi/get_history_operasi'); ?>',
                type: 'POST',
                data: function(d) {
                    d.tanggal_mulai = $('#tanggal_mulai_history').val();
                    d.tanggal_akhir = $('#tanggal_akhir_history').val();
                    d.hari = $('#day-filter-history .btn-day.active').data('day');
                }
            },
            columns: [
                { data: 0, orderable: false, searchable: false },
                { data: 1, orderable: true },
                { data: 2, orderable: true },
                { data: 3, orderable: true },
                { data: 4, orderable: false },
                { data: 5, orderable: true },
                { data: 6, orderable: true },
                { data: 7, orderable: true },
                { data: 8, orderable: true },
                { data: 9, orderable: true },
                { data: 10, orderable: false }
            ],
            order: [[1, 'desc']],
            pageLength: 25,
            responsive: true,
            language: {
                processing: '<div class="loading-spinner"></div> Memuat data...',
                loadingRecords: '<div class="loading-spinner"></div> Memuat...',
                lengthMenu: "Tampilkan _MENU_ data per halaman",
                zeroRecords: "Data tidak ditemukan",
                info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
                infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
                infoFiltered: "(difilter dari _MAX_ total data)",
                search: "Cari:",
                paginate: {
                    first: "Pertama",
                    last: "Terakhir",
                    next: "Selanjutnya",
                    previous: "Sebelumnya"
                }
            }
        });
    }

    // Filter button handlers for Daftar Perjanjian
    $('#btn-filter-daftar').click(function() {
        updateDayButtons('daftar');
        if (tableDaftarPerjanjian) {
            tableDaftarPerjanjian.ajax.reload();
        }
    });

    $('#btn-reset-daftar').click(function() {
        $('#tanggal_mulai_daftar').val('');
        $('#tanggal_akhir_daftar').val('');
        resetDayButtons('daftar');
        if (tableDaftarPerjanjian) {
            tableDaftarPerjanjian.ajax.reload();
        }
    });

    // Filter button handlers for History
    $('#btn-filter-history').click(function() {
        updateDayButtons('history');
        if (tableHistoryOperasi) {
            tableHistoryOperasi.ajax.reload();
        }
    });

    $('#btn-reset-history').click(function() {
        $('#tanggal_mulai_history').val('');
        $('#tanggal_akhir_history').val('');
        resetDayButtons('history');
        if (tableHistoryOperasi) {
            tableHistoryOperasi.ajax.reload();
        }
    });

    // Day filter button handlers
    $(document).on('click', '#day-filter-daftar .btn-day', function(e) {
        e.preventDefault();
        if (!$(this).hasClass('disabled')) {
            $('#day-filter-daftar .btn-day').removeClass('active');
            $(this).addClass('active');
            if (tableDaftarPerjanjian) {
                tableDaftarPerjanjian.ajax.reload();
            }
        }
    });

    $(document).on('click', '#day-filter-history .btn-day', function(e) {
        e.preventDefault();
        if (!$(this).hasClass('disabled')) {
            $('#day-filter-history .btn-day').removeClass('active');
            $(this).addClass('active');
            if (tableHistoryOperasi) {
                tableHistoryOperasi.ajax.reload();
            }
        }
    });

    // Edit button handler
    $(document).on('click', '.btn-edit', function() {
        var id = $(this).data('id');
        var jenis = $(this).data('jenis');

        $.ajax({
            url: '<?php echo base_url('PenjadwalanOperasi/form_penjadwalan'); ?>',
            type: 'POST',
            data: {
                id: id,
                jenis: jenis
            },
            success: function(response) {
                $('#modalFormPenjadwalanBody').html(response);
                $('#modalFormPenjadwalan').modal('show');
            },
            error: function() {
                Swal.fire('Error', 'Gagal memuat form', 'error');
            }
        });
    });

    // Delete button handler
    $(document).on('click', '.btn-delete', function() {
        var id = $(this).data('id');

        Swal.fire({
            title: 'Apakah anda yakin akan menghapus?',
            text: "Data yang dihapus tidak dapat dikembalikan!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Ya',
            cancelButtonText: 'Tidak'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?php echo base_url('PenjadwalanOperasi/delete_penjadwalan'); ?>',
                    type: 'POST',
                    data: { id: id },
                    dataType: 'json',
                    success: function(response) {
                        if (response.status === 'success') {
                            Swal.fire('Berhasil', response.message, 'success');
                            // Reload active table
                            if (currentTab === 'daftar-perjanjian' && tableDaftarPerjanjian) {
                                tableDaftarPerjanjian.ajax.reload();
                            } else if (currentTab === 'operasi-hari-ini' && tableOperasiHariIni) {
                                tableOperasiHariIni.ajax.reload();
                            }
                        } else {
                            Swal.fire('Error', response.message, 'error');
                        }
                    },
                    error: function() {
                        Swal.fire('Error', 'Gagal menghapus data', 'error');
                    }
                });
            }
        });
    });

    // Update day buttons based on date range
    function updateDayButtons(type) {
        var mulai = type === 'daftar' ? $('#tanggal_mulai_daftar').val() : $('#tanggal_mulai_history').val();
        var akhir = type === 'daftar' ? $('#tanggal_akhir_daftar').val() : $('#tanggal_akhir_history').val();

        if (mulai && akhir) {
            $.ajax({
                url: '<?php echo base_url('PenjadwalanOperasi/get_available_days'); ?>',
                type: 'POST',
                data: {
                    tanggal_mulai: mulai,
                    tanggal_akhir: akhir
                },
                dataType: 'json',
                success: function(response) {
                    var container = '#day-filter-' + type;
                    $(container + ' .btn-day').each(function() {
                        var day = $(this).data('day');
                        if (day === 'All') {
                            $(this).removeClass('disabled');
                        } else {
                            if (response[day]) {
                                $(this).removeClass('disabled');
                            } else {
                                $(this).addClass('disabled');
                            }
                        }
                    });
                }
            });
        } else {
            resetDayButtons(type);
        }
    }

    // Reset day buttons
    function resetDayButtons(type) {
        var container = '#day-filter-' + type;
        $(container + ' .btn-day').removeClass('disabled active');
        $(container + ' .btn-day[data-day="All"]').addClass('active');
    }
});
</script>
