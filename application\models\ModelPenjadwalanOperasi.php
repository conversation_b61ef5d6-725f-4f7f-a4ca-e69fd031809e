<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class ModelPenjadwalanOperasi extends CI_Model {

    public function getDataPerjanjian($start, $length, $searchValue, $mulai, $akhir, $hari, $tab)
    {
        $query = "SELECT 
            rmp.ID, 
            ppo.id id_penjadwalan, 
            rr.id id_reservasi, 
            IFNULL(ppo.tgl_operasi, rmp.TANGGAL) tgl_operasi,
            dk.nama kamar_operasi, 
            IFNULL(ppo.created_at, rr.tgl_input) tgl_dibuat, 
            ps.NAMA nama, 
            wlo.norm,
            CONCAT(date_format(ps.TANGGAL_LAHIR, '%d/%m/%Y'), ' <b>(', master.getCariUmurTahun(CURDATE(), ps.TANGGAL_LAHIR), ')</b>') tgl_lahir_umur,
            ppo.menunggu_konfirmasi_ruang, 
            wlo.diagnosis, 
            wlo.<PERSON>,
            master.getNamaLengkapPegawai(d.NIP) dokter_operator, 
            master.getNamaLengkapPegawai(da.NIP) dokter_anestesi,
            tpo.catatan_khusus, 
            rr.status, 
            ppo.status status_penjadwalan,
            IFNULL(
                (
                    SELECT mr.DESKRIPSI ruangan
                    FROM pendaftaran.pendaftaran pp
                        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = pp.NOMOR
                        LEFT JOIN master.ruangan mr ON mr.ID = pk.RUANGAN
                    WHERE ppo.tgl_operasi BETWEEN DATE(pk.MASUK) AND COALESCE(DATE(pk.KELUAR), '9999-12-31')
                        AND pp.NORM = wlo.norm
                        AND mr.JENIS_KUNJUNGAN = 3
                    ORDER BY pk.MASUK ASC
                    LIMIT 1
                ), mr.DESKRIPSI
            ) ruang_rawat,
            CASE 
                WHEN ppo.tujuan_rs IS NOT NULL THEN ref.DESKRIPSI
                WHEN rr.id_cara_bayar = 2 THEN 'Operasi Reguler'
                ELSE 'Operasi Swasta'
            END AS tujuan_rs
        FROM remun_medis.perjanjian rmp
        LEFT JOIN medis.tb_waiting_list_operasi wlo ON wlo.id = rmp.ID_WAITING_LIST_OPERASI
        LEFT JOIN perjanjian.penjadwalan_operasi ppo ON ppo.id_waiting_list_operasi = wlo.id
        LEFT JOIN db_master.tb_kamar dk ON dk.id = ppo.kamar_operasi AND dk.id_ruang IN ('105090101', '105090104')
        LEFT JOIN master.dokter d ON d.ID = wlo.id_dokter
        LEFT JOIN master.dokter da ON da.ID = ppo.dr_anestesi
        LEFT JOIN medis.tb_pendaftaran_operasi tpo ON wlo.id_pendaftaran_operasi = tpo.id
        LEFT JOIN master.pasien ps ON ps.NORM = wlo.norm
        LEFT JOIN db_reservasi.tb_reservasi rr ON rr.id_waiting_list_operasi = wlo.id AND rr.status != 0 AND rr.id_perjanjian = rmp.ID
        LEFT JOIN master.referensi ref ON ref.ID = ppo.tujuan_rs AND ref.jenis = 81
        LEFT JOIN master.ruangan mr ON mr.ID = ppo.ruang_rawat
        WHERE rmp.STATUS != 0
            AND rmp.ID_RUANGAN = '105090104'
            AND wlo.id IS NOT NULL
            AND (ppo.status = 1 OR ppo.id IS NULL)";

        // Filter berdasarkan tanggal
        if (!empty($mulai) && !empty($akhir)) {
            $query .= " AND IFNULL(ppo.tgl_operasi, rmp.TANGGAL) BETWEEN '$mulai' AND '$akhir'";
        }

        // Filter berdasarkan hari
        if (!empty($hari) && $hari != 'All') {
            $query .= " AND DAYNAME(IFNULL(ppo.tgl_operasi, rmp.TANGGAL)) = '$hari'";
        }

        // Filter berdasarkan tab
        if (!empty($tab)) {
            switch ($tab) {
                case 'hari_ini':
                    $query .= " AND IFNULL(ppo.tgl_operasi, rmp.TANGGAL) = CURDATE()";
                    break;
                case 'history':
                    $query .= " AND IFNULL(ppo.tgl_operasi, rmp.TANGGAL) < CURDATE() AND ppo.status = 1";
                    break;
                case 'batal':
                    $query .= " AND (ppo.status = 0 OR rmp.STATUS = 0)";
                    break;
                case 'daftar':
                default:
                    // Default filter untuk daftar perjanjian
                    $query .= " AND IFNULL(ppo.tgl_operasi, rmp.TANGGAL) >= CURDATE()";
                    break;
            }
        }

        // Search
        if (!empty($searchValue)) {
            $query .= " AND (ps.NAMA LIKE '%$searchValue%' OR wlo.norm LIKE '%$searchValue%' OR wlo.diagnosis LIKE '%$searchValue%' OR wlo.tindakan LIKE '%$searchValue%')";
        }

        // Count total records
        $totalQuery = "SELECT COUNT(*) as total FROM ($query) as count_table";
        $totalResult = $this->db->query($totalQuery)->row();
        $recordsTotal = $totalResult->total;
        $recordsFiltered = $recordsTotal;

        // Order and limit
        $query .= " ORDER BY IFNULL(ppo.tgl_operasi, rmp.TANGGAL) DESC";
        if ($length != -1) {
            $query .= " LIMIT $start, $length";
        }

        $result = $this->db->query($query)->result();

        return array(
            'data' => $result,
            'recordsTotal' => $recordsTotal,
            'recordsFiltered' => $recordsFiltered
        );
    }

    public function getDetailPerjanjian($id)
    {
        $query = "SELECT 
            ppo.id AS id_penjadwalan,
            rmp.id AS id_perjanjian,
            rr.id AS id_reservasi,
            wlo.id AS id_waiting_list_operasi,
            tpo.id AS id_tpo,
            mr.JENIS_KELAMIN AS id_jk,
            mr.NORM AS norm,
            master.getNamaLengkap(mr.NORM) AS nama,
            kp.NOMOR AS no_telp,
            wlo.id_dokter,
            tpo.diagnosa_medis AS diagnosis,
            tpo.rencana_tindakan_operasi AS tindakan,
            rr.tgl_rencanaMasuk AS tgl_rawat,
            IF(ppo.tgl_operasi IS NULL, rmp.TANGGAL, ppo.tgl_operasi) AS tgl_operasi,
            ppo.kamar_operasi,
            TIME_FORMAT(ppo.waktu_operasi, '%H:%i') AS waktu_operasi,
            ppo.durasi_operasi,
            ppo.dr_anestesi,
            IF(ppo.jenis_anestesi IS NULL, tpo.rencana_jenis_pembiusan, ppo.jenis_anestesi) AS jenis_anestesi,
            tpo.rencana_jenis_pembiusan_lain,
            ppo.menunggu_konfirmasi_ruang,
            ppo.ruang_rawat,
            IF(ppo.tujuan_rs IS NULL, IF(rr.id_cara_bayar = 2, 2, 16), ppo.tujuan_rs) AS tujuan_rs
        FROM medis.tb_waiting_list_operasi wlo
        LEFT JOIN perjanjian.penjadwalan_operasi ppo ON ppo.id_waiting_list_operasi = wlo.id
        LEFT JOIN remun_medis.perjanjian rmp ON rmp.ID_WAITING_LIST_OPERASI = wlo.id
        LEFT JOIN db_reservasi.tb_reservasi rr ON rr.id_perjanjian = rmp.ID
        LEFT JOIN medis.tb_pendaftaran_operasi tpo ON tpo.id = wlo.id_pendaftaran_operasi
        LEFT JOIN master.pasien mr ON mr.NORM = wlo.norm
        LEFT JOIN master.kontak_pasien kp ON kp.NORM = mr.NORM AND kp.JENIS = 3
        WHERE rmp.ID_RUANGAN = '105090104' AND rmp.ID = '$id'";

        return $this->db->query($query)->row_array();
    }

    public function simpanPerjanjian($data)
    {
        $this->db->trans_start();

        try {
            if (!empty($data['id_perjanjian'])) {
                // Update existing
                $this->updatePerjanjian($data);
            } else {
                // Insert new
                $this->insertPerjanjian($data);
            }

            $this->db->trans_complete();
            return $this->db->trans_status();
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return false;
        }
    }

    private function insertPerjanjian($data)
    {
        // Insert perjanjian
        $perjanjian_data = array(
            'ID_WAITING_LIST_OPERASI' => $data['id_waiting_list'],
            'ID_RUANGAN' => '105090104',
            'TANGGAL' => $data['tgl_operasi'],
            'STATUS' => 1,
            'created_at' => date('Y-m-d H:i:s'),
            'created_by' => $this->session->userdata('id')
        );
        $this->db->insert('remun_medis.perjanjian', $perjanjian_data);
        $id_perjanjian = $this->db->insert_id();

        // Insert penjadwalan operasi
        $jadwal_data = array(
            'id_waiting_list_operasi' => $data['id_waiting_list'],
            'kamar_operasi' => $data['kamar_operasi'],
            'tgl_operasi' => $data['tgl_operasi'],
            'waktu_operasi' => $data['waktu_operasi'],
            'durasi_operasi' => $data['durasi_operasi'],
            'dr_anestesi' => !empty($data['dr_anestesi']) ? $data['dr_anestesi'] : null,
            'jenis_anestesi' => !empty($data['jenis_anestesi']) ? $data['jenis_anestesi'] : null,
            'menunggu_konfirmasi_ruang' => !empty($data['menunggu_konfirmasi_ruang']) ? $data['menunggu_konfirmasi_ruang'] : null,
            'ruang_rawat' => !empty($data['ruang_rawat']) ? $data['ruang_rawat'] : null,
            'tujuan_rs' => $data['tujuan_rs'],
            'status' => 1,
            'created_at' => date('Y-m-d H:i:s'),
            'created_by' => $this->session->userdata('id')
        );
        $this->db->insert('perjanjian.penjadwalan_operasi', $jadwal_data);
    }

    private function updatePerjanjian($data)
    {
        // Update perjanjian
        $perjanjian_data = array(
            'TANGGAL' => $data['tgl_operasi'],
            'updated_at' => date('Y-m-d H:i:s'),
            'updated_by' => $this->session->userdata('id')
        );
        $this->db->where('ID', $data['id_perjanjian']);
        $this->db->update('remun_medis.perjanjian', $perjanjian_data);

        // Update penjadwalan operasi
        $jadwal_data = array(
            'kamar_operasi' => $data['kamar_operasi'],
            'tgl_operasi' => $data['tgl_operasi'],
            'waktu_operasi' => $data['waktu_operasi'],
            'durasi_operasi' => $data['durasi_operasi'],
            'dr_anestesi' => !empty($data['dr_anestesi']) ? $data['dr_anestesi'] : null,
            'jenis_anestesi' => !empty($data['jenis_anestesi']) ? $data['jenis_anestesi'] : null,
            'menunggu_konfirmasi_ruang' => !empty($data['menunggu_konfirmasi_ruang']) ? $data['menunggu_konfirmasi_ruang'] : null,
            'ruang_rawat' => !empty($data['ruang_rawat']) ? $data['ruang_rawat'] : null,
            'tujuan_rs' => $data['tujuan_rs'],
            'updated_at' => date('Y-m-d H:i:s'),
            'updated_by' => $this->session->userdata('id')
        );
        $this->db->where('id', $data['id_penjadwalan']);
        $this->db->update('perjanjian.penjadwalan_operasi', $jadwal_data);
    }

    public function hapusPerjanjian($id, $alasan)
    {
        $this->db->trans_start();

        // Soft delete penjadwalan operasi
        $this->db->where('id_waiting_list_operasi', "(SELECT ID_WAITING_LIST_OPERASI FROM remun_medis.perjanjian WHERE ID = '$id')", false);
        $this->db->update('perjanjian.penjadwalan_operasi', array(
            'status' => 0,
            'alasan_batal' => $alasan,
            'cancel_at' => date('Y-m-d H:i:s'),
            'cancel_by' => $this->session->userdata('id')
        ));

        // Soft delete perjanjian
        $this->db->where('ID', $id);
        $this->db->update('remun_medis.perjanjian', array(
            'STATUS' => 0,
            'alasan_batal' => $alasan,
            'cancel_at' => date('Y-m-d H:i:s'),
            'cancel_by' => $this->session->userdata('id')
        ));

        $this->db->trans_complete();
        return $this->db->trans_status();
    }

    public function getPasien($search = '')
    {
        $query = "SELECT DISTINCT p.NORM as norm, p.NAMA as nama 
                  FROM master.pasien p 
                  INNER JOIN medis.tb_waiting_list_operasi wlo ON wlo.norm = p.NORM 
                  WHERE 1=1";
        
        if (!empty($search)) {
            $query .= " AND (p.NORM LIKE '%$search%' OR p.NAMA LIKE '%$search%')";
        }
        
        $query .= " ORDER BY p.NAMA ASC LIMIT 20";
        
        return $this->db->query($query)->result_array();
    }

    public function getPasienDetail($norm)
    {
        $query = "SELECT p.NAMA as nama, p.JENIS_KELAMIN as jk, p.TANGGAL_LAHIR as tgl_lahir, 
                         kp.NOMOR as telepon
                  FROM master.pasien p
                  LEFT JOIN master.kontak_pasien kp ON kp.NORM = p.NORM AND kp.JENIS = 3
                  WHERE p.NORM = '$norm'";
        
        return $this->db->query($query)->row_array();
    }

    public function getDokter()
    {
        $query = "SELECT ID as id, master.getNamaLengkapPegawai(NIP) as nama 
                  FROM master.dokter 
                  WHERE STATUS = 1 
                  ORDER BY nama ASC";
        
        return $this->db->query($query)->result_array();
    }

    public function getKamarOperasi()
    {
        $query = "SELECT id, nama 
                  FROM db_master.tb_kamar 
                  WHERE id_ruang IN ('105090101', '105090104') 
                  ORDER BY nama ASC";
        
        return $this->db->query($query)->result_array();
    }

    public function getDokterAnestesi()
    {
        $query = "SELECT ID as id, master.getNamaLengkapPegawai(NIP) as nama 
                  FROM master.dokter 
                  WHERE STATUS = 1 
                  ORDER BY nama ASC";
        
        return $this->db->query($query)->result_array();
    }

    public function getJenisAnestesi()
    {
        $query = "SELECT ID as id, DESKRIPSI as nama 
                  FROM master.referensi 
                  WHERE jenis = 80 
                  ORDER BY DESKRIPSI ASC";
        
        return $this->db->query($query)->result_array();
    }

    public function getRuangRawat()
    {
        $query = "SELECT ID as id, DESKRIPSI as nama
                  FROM master.ruangan
                  WHERE JENIS_KUNJUNGAN = 3
                  ORDER BY DESKRIPSI ASC";

        return $this->db->query($query)->result_array();
    }

    // Method untuk tab Daftar Perjanjian Operasi (Query 1)
    public function getDaftarPerjanjian($start, $length, $searchValue, $mulai, $akhir, $hari, $orderBy, $orderDir)
    {
        $this->db->select(
            "rmp.ID, ppo.id id_penjadwalan, rr.id id_reservasi, IFNULL(ppo.tgl_operasi, rmp.TANGGAL) tgl_operasi,
            dk.nama kamar_operasi, IFNULL(ppo.created_at, rr.tgl_input) tgl_dibuat, ps.NAMA nama, wlo.norm,
            CONCAT(date_format(ps.TANGGAL_LAHIR, '%d/%m/%Y'), ' <b>(', master.getCariUmurTahun(CURDATE(), ps.TANGGAL_LAHIR), ')</b>') tgl_lahir_umur,
            ppo.menunggu_konfirmasi_ruang, wlo.diagnosis, wlo.tindakan,
            master.getNamaLengkapPegawai(d.NIP) dokter_operator, master.getNamaLengkapPegawai(da.NIP) dokter_anestesi,
            tpo.catatan_khusus, rr.status, ppo.status status_penjadwalan,tpo.id id_tpo,
            IFNULL(
                (
                    SELECT mr.DESKRIPSI ruangan
                    FROM pendaftaran.pendaftaran pp
                        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = pp.NOMOR
                        LEFT JOIN master.ruangan mr ON mr.ID = pk.RUANGAN
                    WHERE ppo.tgl_operasi BETWEEN DATE(pk.MASUK) AND COALESCE(DATE(pk.KELUAR), '9999-12-31')
                        AND pp.NORM = wlo.norm
                        AND mr.JENIS_KUNJUNGAN = 3
                    ORDER BY pk.MASUK ASC
                    LIMIT 1
                ), mr.DESKRIPSI
            ) ruang_rawat,
             CASE
                WHEN ppo.tujuan_rs IS NOT NULL THEN ref.DESKRIPSI
                WHEN rr.id_cara_bayar = 2 THEN 'Operasi Reguler'
                ELSE 'Operasi Swasta'
            END AS tujuan_rs"
        );
        $this->db->from('remun_medis.perjanjian rmp');
        $this->db->join('medis.tb_waiting_list_operasi wlo', 'wlo.id = rmp.ID_WAITING_LIST_OPERASI', 'left');
        $this->db->join('perjanjian.penjadwalan_operasi ppo', 'ppo.id_waiting_list_operasi = wlo.id', 'left');
        $this->db->join('db_master.tb_kamar dk', "dk.id = ppo.kamar_operasi AND dk.id_ruang = '105090104'", 'left');
        $this->db->join('master.dokter d', 'd.ID = wlo.id_dokter', 'left');
        $this->db->join('master.dokter da', 'da.ID = ppo.dr_anestesi', 'left');
        $this->db->join('medis.tb_pendaftaran_operasi tpo', 'wlo.id_pendaftaran_operasi = tpo.id', 'left');
        $this->db->join('master.pasien ps', 'ps.NORM = wlo.norm', 'left');
        $this->db->join(
            'db_reservasi.tb_reservasi rr',
            'rr.id_waiting_list_operasi = wlo.id AND rr.status != 0 AND rr.id_perjanjian = rmp.ID',
            'left'
        );
        $this->db->join('master.referensi ref', 'ref.ID = ppo.tujuan_rs AND ref.jenis = 81', 'left');
        $this->db->join('master.ruangan mr', 'mr.ID = ppo.ruang_rawat', 'left');
        $this->db->where('rmp.STATUS !=', 0);
        $this->db->where('rmp.ID_RUANGAN', '105090104');
        $this->db->where('wlo.id IS NOT NULL');
        $this->db->where('(ppo.status = 1 OR ppo.id IS NULL)');

        // Filter tanggal
        if ($mulai != null) {
            $this->db->where('rmp.TANGGAL >=', $mulai);
        }
        if ($akhir != null) {
            $this->db->where('rmp.TANGGAL <=', $akhir);
        } else {
            $this->db->where('rmp.TANGGAL <= CURDATE()');
        }

        // Filter hari
        if ($hari != 'All') {
            $this->db->where('DAYNAME(rmp.TANGGAL)', $hari);
        }

        // Search
        if (!empty($searchValue)) {
            $this->db->group_start();
            $this->db->like('ps.NAMA', $searchValue);
            $this->db->or_like('wlo.norm', $searchValue);
            $this->db->or_like('wlo.diagnosis', $searchValue);
            $this->db->or_like('wlo.tindakan', $searchValue);
            $this->db->group_end();
        }

        // Count total records
        $totalQuery = clone $this->db;
        $recordsTotal = $totalQuery->count_all_results();
        $recordsFiltered = $recordsTotal;

        // Order and limit
        if ($orderBy && $orderBy != 'no') {
            $this->db->order_by($orderBy, $orderDir);
        }
        $this->db->limit($length, $start);

        $query = $this->db->get();
        $data = $query->result();

        return array(
            'data' => $data,
            'recordsTotal' => $recordsTotal,
            'recordsFiltered' => $recordsFiltered
        );
    }

    // Method untuk tab Operasi Hari Ini (Query 2)
    public function getOperasiHariIni($start, $length, $searchValue, $orderBy, $orderDir)
    {
        $this->db->select(
            "rmp.ID, ppo.id id_penjadwalan, rr.id id_reservasi, IFNULL(ppo.tgl_operasi, rmp.TANGGAL) tgl_operasi,
            dk.nama kamar_operasi, IFNULL(ppo.created_at, rr.tgl_input) tgl_dibuat, ps.NAMA nama, wlo.norm,
            CONCAT(date_format(ps.TANGGAL_LAHIR, '%d/%m/%Y'), ' <b>(', master.getCariUmurTahun(CURDATE(), ps.TANGGAL_LAHIR), ')</b>') tgl_lahir_umur,
            ppo.menunggu_konfirmasi_ruang, wlo.diagnosis, wlo.tindakan,
            master.getNamaLengkapPegawai(d.NIP) dokter_operator, master.getNamaLengkapPegawai(da.NIP) dokter_anestesi,
            tpo.catatan_khusus, rr.status, ppo.status status_penjadwalan,
            IFNULL(
                (
                    SELECT mr.DESKRIPSI ruangan
                    FROM pendaftaran.pendaftaran pp
                        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = pp.NOMOR
                        LEFT JOIN master.ruangan mr ON mr.ID = pk.RUANGAN
                    WHERE ppo.tgl_operasi BETWEEN DATE(pk.MASUK) AND COALESCE(DATE(pk.KELUAR), '9999-12-31')
                        AND pp.NORM = wlo.norm
                        AND mr.JENIS_KUNJUNGAN = 3
                    ORDER BY pk.MASUK ASC
                    LIMIT 1
                ), mr.DESKRIPSI
            ) ruang_rawat,
            mr2.DESKRIPSI ruang_rawat_desc,
             CASE
                WHEN ppo.tujuan_rs IS NOT NULL THEN ref.DESKRIPSI
                WHEN rr.id_cara_bayar = 2 THEN 'Operasi Reguler'
                ELSE 'Operasi Swasta'
            END AS tujuan_rs"
        );
        $this->db->from('remun_medis.perjanjian rmp');
        $this->db->join('medis.tb_waiting_list_operasi wlo', 'wlo.id = rmp.ID_WAITING_LIST_OPERASI', 'left');
        $this->db->join('perjanjian.penjadwalan_operasi ppo', 'ppo.id_waiting_list_operasi = wlo.id', 'left');
        $this->db->join('db_master.tb_kamar dk', "dk.id = ppo.kamar_operasi AND dk.id_ruang = '105090104'", 'left');
        $this->db->join('master.dokter d', 'd.ID = wlo.id_dokter', 'left');
        $this->db->join('master.dokter da', 'da.ID = ppo.dr_anestesi', 'left');
        $this->db->join('medis.tb_pendaftaran_operasi tpo', 'wlo.id_pendaftaran_operasi = tpo.id', 'left');
        $this->db->join('master.pasien ps', 'ps.NORM = wlo.norm', 'left');
        $this->db->join(
            'db_reservasi.tb_reservasi rr',
            'rr.id_waiting_list_operasi = wlo.id AND rr.status != 0 AND rr.id_perjanjian = rmp.ID',
            'left'
        );
        $this->db->join('master.ruangan mr', 'mr.ID = ppo.ruang_rawat', 'left');
        $this->db->join('master.referensi ref', 'ref.ID = ppo.tujuan_rs AND ref.jenis = 81', 'left');
        $this->db->join('master.ruangan mr2', 'mr2.ID = rmp.ID_RUANGAN', 'left');
        $this->db->where('rmp.STATUS', 1);
        $this->db->where('rmp.ID_RUANGAN', '105090104');

        $this->db->group_start();
        // ppo.id IS NULL → rr.id_cara_bayar != 2
        $this->db->group_start();
        $this->db->where('ppo.id IS NULL', null, false);
        $this->db->where('rr.id_cara_bayar !=', 2);
        $this->db->group_end();
        // ppo.id IS NOT NULL → ppo.tujuan_rs = 16
        $this->db->or_group_start();
        $this->db->where('ppo.id IS NOT NULL', null, false);
        $this->db->where('ppo.tujuan_rs', 16);
        $this->db->group_end();
        $this->db->group_end();

        $this->db->where('rmp.TANGGAL = CURDATE()');
        $this->db->where('ppo.status !=', 0);
        $this->db->where('ppo.status !=', 5);

        // Search
        if (!empty($searchValue)) {
            $this->db->group_start();
            $this->db->like('ps.NAMA', $searchValue);
            $this->db->or_like('wlo.norm', $searchValue);
            $this->db->or_like('wlo.diagnosis', $searchValue);
            $this->db->or_like('wlo.tindakan', $searchValue);
            $this->db->group_end();
        }

        // Count total records
        $totalQuery = clone $this->db;
        $recordsTotal = $totalQuery->count_all_results();
        $recordsFiltered = $recordsTotal;

        // Order and limit
        if ($orderBy && $orderBy != 'no') {
            $this->db->order_by($orderBy, $orderDir);
        }
        $this->db->limit($length, $start);

        $query = $this->db->get();
        $data = $query->result();

        return array(
            'data' => $data,
            'recordsTotal' => $recordsTotal,
            'recordsFiltered' => $recordsFiltered
        );
    }

    // Method untuk tab History/Selesai Operasi (Query 3)
    public function getHistoryOperasi($start, $length, $searchValue, $mulai, $akhir, $hari, $orderBy, $orderDir)
    {
        $this->db->select(
            "rmp.ID, ppo.id id_penjadwalan, rr.id id_reservasi, IFNULL(rmp.TANGGAL, ppo.tgl_operasi) tgl_operasi,
            dk.nama kamar_operasi, IFNULL(rr.tgl_input, ppo.created_at) tgl_dibuat, ps.NAMA nama, wlo.norm,
            CONCAT(date_format(ps.TANGGAL_LAHIR, '%d/%m/%Y'), ' <b>(', master.getCariUmurTahun(CURDATE(), ps.TANGGAL_LAHIR), ')</b>') tgl_lahir_umur,
            ppo.menunggu_konfirmasi_ruang, wlo.diagnosis, wlo.tindakan,
            master.getNamaLengkapPegawai(d.NIP) dokter_operator, master.getNamaLengkapPegawai(da.NIP) dokter_anestesi,
            tpo.catatan_khusus, rr.status, ppo.status status_penjadwalan,
            IFNULL(
                (
                    SELECT mr.DESKRIPSI ruangan
                    FROM pendaftaran.pendaftaran pp
                        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = pp.NOMOR
                        LEFT JOIN master.ruangan mr ON mr.ID = pk.RUANGAN
                    WHERE ppo.tgl_operasi BETWEEN DATE(pk.MASUK) AND COALESCE(DATE(pk.KELUAR), '9999-12-31')
                        AND pp.NORM = wlo.norm
                        AND mr.JENIS_KUNJUNGAN = 3
                    ORDER BY pk.MASUK ASC
                    LIMIT 1
                ), mr.DESKRIPSI
            ) ruang_rawat,
            CASE
                WHEN ppo.tujuan_rs IS NOT NULL THEN ref.DESKRIPSI
                WHEN rr.id_cara_bayar = 2 THEN 'Operasi Reguler'
                ELSE 'Operasi Swasta'
            END AS tujuan_rs"
        );
        $this->db->from('remun_medis.perjanjian rmp');
        $this->db->join('medis.tb_waiting_list_operasi wlo', 'wlo.id = rmp.ID_WAITING_LIST_OPERASI', 'left');
        $this->db->join('perjanjian.penjadwalan_operasi ppo', 'ppo.id_waiting_list_operasi = wlo.id', 'left');
        $this->db->join('db_master.tb_kamar dk', "dk.id = ppo.kamar_operasi AND dk.id_ruang = '105090104'", 'left');
        $this->db->join('master.dokter d', 'd.ID = wlo.id_dokter', 'left');
        $this->db->join('master.dokter da', 'da.ID = ppo.dr_anestesi', 'left');
        $this->db->join('medis.tb_pendaftaran_operasi tpo', 'wlo.id_pendaftaran_operasi = tpo.id', 'left');
        $this->db->join('master.pasien ps', 'ps.NORM = wlo.norm', 'left');
        $this->db->join(
            'db_reservasi.tb_reservasi rr',
            'rr.id_waiting_list_operasi = wlo.id AND rr.status != 0 AND rr.id_perjanjian = rmp.ID',
            'left'
        );
        $this->db->join('master.ruangan mr', 'mr.ID = ppo.ruang_rawat', 'left');
        $this->db->join('master.referensi ref', 'ref.ID = ppo.tujuan_rs AND ref.jenis = 81', 'left');

        $this->db->where('rmp.STATUS', 1);
        $this->db->where_in('ppo.status', array(0, 5));
        $this->db->where('rmp.ID_RUANGAN', '105090104');

        // Filter tanggal
        if ($mulai != null) {
            $this->db->where('rmp.TANGGAL >=', $mulai);
        }
        if ($akhir != null) {
            $this->db->where('rmp.TANGGAL <=', $akhir);
        } else {
            $this->db->where('rmp.TANGGAL <= CURDATE()');
        }

        // Filter hari
        if ($hari != 'All') {
            $this->db->where('DAYNAME(ppo.tgl_operasi)', $hari);
        }

        // Search
        if (!empty($searchValue)) {
            $this->db->group_start();
            $this->db->like('ps.NAMA', $searchValue);
            $this->db->or_like('wlo.norm', $searchValue);
            $this->db->or_like('wlo.diagnosis', $searchValue);
            $this->db->or_like('wlo.tindakan', $searchValue);
            $this->db->group_end();
        }

        // Count total records
        $totalQuery = clone $this->db;
        $recordsTotal = $totalQuery->count_all_results();
        $recordsFiltered = $recordsTotal;

        // Order and limit
        if ($orderBy && $orderBy != 'no') {
            $this->db->order_by($orderBy, $orderDir);
        }
        $this->db->limit($length, $start);

        $query = $this->db->get();
        $data = $query->result();

        return array(
            'data' => $data,
            'recordsTotal' => $recordsTotal,
            'recordsFiltered' => $recordsFiltered
        );
    }

    // Method untuk mendapatkan hari yang tersedia dalam rentang tanggal
    public function getAvailableDays($mulai, $akhir)
    {
        $available_days = array();

        if ($mulai && $akhir) {
            $start_date = new DateTime($mulai);
            $end_date = new DateTime($akhir);

            $days_in_range = array();

            while ($start_date <= $end_date) {
                $day_name = $start_date->format('l'); // Monday, Tuesday, etc.
                $days_in_range[] = $day_name;
                $start_date->add(new DateInterval('P1D'));
            }

            $unique_days = array_unique($days_in_range);

            $day_mapping = array(
                'Monday' => 'Monday',
                'Tuesday' => 'Tuesday',
                'Wednesday' => 'Wednesday',
                'Thursday' => 'Thursday',
                'Friday' => 'Friday',
                'Saturday' => 'Saturday',
                'Sunday' => 'Sunday'
            );

            foreach ($day_mapping as $english => $indonesian) {
                $available_days[$english] = in_array($english, $unique_days);
            }
        }

        return $available_days;
    }

    // Method isiForm untuk mengambil data form
    public function isiForm($jenis, $ID)
    {
        $this->db->select([
            'ppo.id AS id_penjadwalan',
            'rmp.id AS id_perjanjian',
            'rr.id AS id_reservasi',
            'wlo.id AS id_waiting_list_operasi',
            'tpo.id AS id_tpo',
            'mr.JENIS_KELAMIN AS id_jk',
            'mr.NORM AS norm',
            'master.getNamaLengkap(mr.NORM) AS nama',
            'kp.NOMOR AS no_telp',
            'wlo.id_dokter',
            'tpo.diagnosa_medis AS diagnosis',
            'tpo.rencana_tindakan_operasi AS tindakan',
            'IF(rr.tgl_rencanaMasuk IS NULL, "-", rr.tgl_rencanaMasuk) AS tgl_rawat',
            'IF(ppo.tgl_operasi IS NULL, rmp.TANGGAL, ppo.tgl_operasi) AS tgl_operasi',
            'ppo.kamar_operasi',
            'TIME_FORMAT(ppo.waktu_operasi, "%H:%i") AS waktu_operasi',
            'ppo.durasi_operasi',
            'ppo.dr_anestesi',
            'IF(ppo.jenis_anestesi IS NULL, tpo.rencana_jenis_pembiusan, ppo.jenis_anestesi) AS jenis_anestesi',
            'tpo.rencana_jenis_pembiusan_lain',
            'ppo.menunggu_konfirmasi_ruang',
            'ppo.ruang_rawat',
            'tpo.nokun',
            'tpo.diagnosa_medis',
            'tpo.tanggal_operasi',
            'tpo.rencana_jenis_pembiusan',
            'tpo.perkiraan_lama_operasi',
            'tpo.potong_beku',
            'tpo.join_operasi',
            'tpo.sifat_operasi',
            'IF(ppo.tujuan_rs IS NULL, 16, ppo.tujuan_rs) AS tujuan_rs',
            'rr.id_cara_bayar',
            'rr.id_kelas'
        ]);

        $this->db->from('medis.tb_waiting_list_operasi wlo');
        $this->db->join('perjanjian.penjadwalan_operasi ppo', 'ppo.id_waiting_list_operasi = wlo.id', 'left');
        $this->db->join('remun_medis.perjanjian rmp', 'rmp.ID_WAITING_LIST_OPERASI = wlo.id', 'left');
        $this->db->join('db_reservasi.tb_reservasi rr', 'rr.id_perjanjian = rmp.ID', 'left');
        $this->db->join('medis.tb_pendaftaran_operasi tpo', 'tpo.id = wlo.id_pendaftaran_operasi', 'left');
        $this->db->join('master.pasien mr', 'mr.NORM = wlo.norm', 'left');
        $this->db->join('master.kontak_pasien kp', 'kp.NORM = mr.NORM AND kp.JENIS = 3', 'left');

        if ($jenis == 'buat') {
            $this->db->where('ppo.id', $ID);
        } elseif ($jenis == 'edit') {
            $this->db->where('tpo.id', $ID);
        }

        $query = $this->db->get();
        return $query->row_array();
    }

    // Method dokter
    public function dokter()
    {
        $this->db->select(
            'd.ID id_dokter, master.getNamaLengkapPegawai(d.NIP) dokter, p.SMF id_smf, smf.DESKRIPSI smf'
        );
        $this->db->from('master.dokter d');
        $this->db->join('master.pegawai p', 'p.NIP = d.NIP', 'left');
        $this->db->join('master.referensi smf', 'smf.ID = p.SMF AND smf.JENIS = 26', 'left');
        $this->db->where('d.STATUS', 1);
        $this->db->order_by('p.NAMA', 'asc');
        $query = $this->db->get();
        return $query->result_array();
    }

    // Method kamar
    public function kamar($id_ruang)
    {
        $this->db->select('id, nama, status');
        $this->db->from('db_master.tb_kamar');
        $this->db->where('id_ruang', $id_ruang);
        $this->db->where('status', 1);
        $this->db->order_by('nama', 'asc');
        $query = $this->db->get();
        return $query->result_array();
    }

    // Method dokter_anestesi
    public function dokter_anestesi()
    {
        $this->db->select(
            'd.ID id_dokter, master.getNamaLengkapPegawai(d.NIP) dokter, p.SMF id_smf, smf.DESKRIPSI smf'
        );
        $this->db->from('master.dokter d');
        $this->db->join('master.pegawai p', 'p.NIP = d.NIP', 'left');
        $this->db->join('master.referensi smf', 'smf.ID = p.SMF AND smf.JENIS = 26', 'left');
        $this->db->where_in('smf.ID', [6, 46, 55]);
        $this->db->where('d.STATUS', 1);
        $this->db->order_by('p.NAMA', 'asc');
        $query = $this->db->get();
        return $query->result_array();
    }

    // Method referensi
    public function referensi($id)
    {
        $this->db->select('id_variabel, variabel, nilai, status, status_checked');
        $this->db->from('db_master.variabel');
        $this->db->where('id_referensi', $id);
        $this->db->order_by('id_variabel', 'asc');
        $this->db->order_by('seq', 'asc');
        $this->db->where('status !=', 0);
        $query = $this->db->get();
        return $query->result_array();
    }

    // Method deletePenjadwalan
    public function deletePenjadwalan($id)
    {
        $this->db->where('id', $id);
        return $this->db->delete('perjanjian.penjadwalan_operasi');
    }

    // Method getCalendarData - Query jadwal sesuai dokumentasi
    public function getCalendarData($start_date, $end_date)
    {
        $query = "SELECT
            c.status AS status_jadwal,
            c.id AS id_penjadwalan,
            b.ID AS perjanjian,
            c.tgl_operasi AS tanggal_operasi,
            c.waktu_operasi,
            c.slot_operasi,
            c.kamar_operasi,
            CONCAT(b.NAMAPASIEN, ' [', b.NOMR, ']') AS pasien_info,
            db_rekammedis.getNamaLengkapDokter(a.dokter_bedah) AS nama_dokter,
            a.rencana_tindakan_operasi,
            CONCAT(
                TIME_FORMAT(c.waktu_operasi, '%H:%i'),
                ' - ',
                DATE_FORMAT(
                    ADDTIME(
                        STR_TO_DATE(c.waktu_operasi, '%H:%i'),
                        SEC_TO_TIME(c.durasi_operasi * 60)
                    ),
                    '%H:%i'
                )
            ) AS waktu_selesai,
            DATE_FORMAT(
                ADDTIME(
                    STR_TO_DATE(c.waktu_operasi, '%H:%i'),
                    SEC_TO_TIME((c.durasi_operasi + 30) * 60)
                ),
                '%H:%i'
            ) AS waktu_selesai_plus_interval,
            TIME_FORMAT(c.waktu_operasi, '%H:%i') AS jam_mulai_sort
        FROM perjanjian.penjadwalan_operasi c
        LEFT JOIN remun_medis.perjanjian b
            ON c.id_perjanjian = b.ID
            AND b.ID_RUANGAN = '105090104'
        LEFT JOIN medis.tb_pendaftaran_operasi a
            ON b.ID_PENDAFTARAN_OPERASI = a.id
        WHERE
            b.ID_RUANGAN = '105090104'
            AND a.status != 0
            AND b.STATUS != 0
            AND c.STATUS != 0
            AND c.tgl_operasi BETWEEN ? AND ?
        ORDER BY c.tgl_operasi ASC, c.waktu_operasi ASC";

        $result = $this->db->query($query, array($start_date, $end_date))->result_array();

        // Organize data by date and slot
        $calendar_data = array();
        foreach ($result as $row) {
            $date = $row['tanggal_operasi'];
            $slot = $row['slot_operasi'];
            $kamar = $row['kamar_operasi'];

            if (!isset($calendar_data[$date])) {
                $calendar_data[$date] = array();
            }

            // Determine slot key based on kamar and slot number
            if ($kamar == '105090101') { // OKA 1
                $slot_key = 'oka1_slot' . $slot;
            } else { // OKA 2
                $slot_key = 'oka2_slot' . ($slot - 4);
            }

            $calendar_data[$date][$slot_key] = $row;
        }

        return $calendar_data;
    }

    // Method savePenjadwalan
    public function savePenjadwalan($data)
    {
        if (isset($data['id_penjadwalan']) && !empty($data['id_penjadwalan'])) {
            // Update existing record
            $this->db->where('id', $data['id_penjadwalan']);
            unset($data['id_penjadwalan']); // Remove ID from data array
            return $this->db->update('perjanjian.penjadwalan_operasi', $data);
        } else {
            // Insert new record
            unset($data['id_penjadwalan']); // Remove empty ID
            $data['created_at'] = date('Y-m-d H:i:s');
            return $this->db->insert('perjanjian.penjadwalan_operasi', $data);
        }
    }
}
