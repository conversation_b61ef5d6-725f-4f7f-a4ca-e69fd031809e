<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON>jadwalanOperasi extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        if($this->session->userdata('logged_in') == FALSE ){
            redirect('login');  
        }
        
        // Check specific permission
        if($this->session->userdata('admision') != '1' || $this->session->userdata('stat_admision') != '2') {
            show_404();
        }
        
        date_default_timezone_set("Asia/Bangkok");
        $this->load->model(array('ModelPenjadwalanOperasi'));
    }

    public function index(){
        $data = array(
            'isi' => 'PenjadwalanOperasi/Index'
        );
        $this->load->view('Layout/Wrapper', $data);
    }

    public function get_data()
    {
        $draw = intval($this->input->post("draw"));
        $start = $this->input->post('start');  
        $length = $this->input->post('length'); 
        $searchValue = $this->input->post('search')['value'];
        $mulai = $this->input->post('mulai');
        $akhir = $this->input->post('akhir');
        $hari = $this->input->post('hari');
        $tujuan_rs = $this->input->post('tujuan_rs');
        
        $tab = $this->input->post('tab');
        $listdata = $this->ModelPenjadwalanOperasi->getDataPerjanjian($start, $length, $searchValue, $mulai, $akhir, $hari, $tab);
        
        $data = array();
        $no = $start + 1;
        foreach ($listdata['data'] as $row) {
            $sub_array = array();
            $sub_array[] = $no++;
            $sub_array[] = date('d/m/Y', strtotime($row->tgl_operasi));
            $sub_array[] = $row->kamar_operasi ?: '-';
            $sub_array[] = $row->tujuan_rs;
            $sub_array[] = date('d/m/Y H:i', strtotime($row->tgl_dibuat));
            $sub_array[] = $row->nama . '<br><small class="text-muted">' . $row->norm . '</small>';
            $sub_array[] = $row->norm;
            $sub_array[] = $row->tgl_lahir_umur;
            $sub_array[] = $row->ruang_rawat ?: '-';
            $sub_array[] = $row->diagnosis ?: '-';
            $sub_array[] = $row->tindakan ?: '-';
            $sub_array[] = $row->dokter_operator ?: '-';
            $sub_array[] = $row->dokter_anestesi ?: '-';
            $sub_array[] = $row->catatan_khusus ?: '-';
            
            // Tombol aksi
            $aksi = '';
            if ($row->id_penjadwalan) {
                $aksi .= '<button type="button" class="btn btn-sm btn-primary mr-1" onclick="editPerjanjian(' . $row->ID . ')" title="Edit">
                            <i class="fe-edit"></i>
                          </button>';
                $aksi .= '<button type="button" class="btn btn-sm btn-danger" onclick="hapusPerjanjian(' . $row->ID . ')" title="Hapus">
                            <i class="fe-trash-2"></i>
                          </button>';
            } else {
                $aksi .= '<button type="button" class="btn btn-sm btn-success" onclick="buatJadwal(' . $row->ID . ')" title="Buat Jadwal">
                            <i class="fe-plus"></i> Jadwal
                          </button>';
            }
            
            $sub_array[] = $aksi;
            $data[] = $sub_array;
        }

        $output = array(
            "draw" => $draw,
            "recordsTotal" => $listdata['recordsTotal'],
            "recordsFiltered" => $listdata['recordsFiltered'],
            "data" => $data
        );

        echo json_encode($output);
    }

    public function get_detail($id)
    {
        $data = $this->ModelPenjadwalanOperasi->getDetailPerjanjian($id);
        echo json_encode($data);
    }

    public function simpan()
    {
        $this->form_validation->set_rules($this->_validation_rules());
        
        if ($this->form_validation->run() == FALSE) {
            $response = array(
                'status' => 'error',
                'message' => validation_errors()
            );
        } else {
            $data = $this->input->post();
            
            if ($this->ModelPenjadwalanOperasi->simpanPerjanjian($data)) {
                $response = array(
                    'status' => 'success',
                    'message' => 'Data berhasil disimpan'
                );
            } else {
                $response = array(
                    'status' => 'error',
                    'message' => 'Gagal menyimpan data'
                );
            }
        }
        
        echo json_encode($response);
    }

    public function hapus()
    {
        $id = $this->input->post('id');
        $alasan = $this->input->post('alasan');
        
        if (empty($alasan)) {
            $response = array(
                'status' => 'error',
                'message' => 'Alasan penghapusan wajib diisi'
            );
        } else {
            if ($this->ModelPenjadwalanOperasi->hapusPerjanjian($id, $alasan)) {
                $response = array(
                    'status' => 'success',
                    'message' => 'Data berhasil dihapus'
                );
            } else {
                $response = array(
                    'status' => 'error',
                    'message' => 'Gagal menghapus data'
                );
            }
        }
        
        echo json_encode($response);
    }

    public function get_pasien()
    {
        $search = $this->input->get('q');
        $data = $this->ModelPenjadwalanOperasi->getPasien($search);
        
        $results = array();
        foreach ($data as $row) {
            $results[] = array(
                'id' => $row['norm'],
                'text' => $row['norm'] . ' - ' . $row['nama']
            );
        }
        
        echo json_encode(array('results' => $results));
    }

    public function get_pasien_detail($norm)
    {
        $data = $this->ModelPenjadwalanOperasi->getPasienDetail($norm);
        echo json_encode($data);
    }

    public function get_dokter()
    {
        $data = $this->ModelPenjadwalanOperasi->getDokter();
        echo json_encode($data);
    }

    public function get_kamar()
    {
        $data = $this->ModelPenjadwalanOperasi->getKamarOperasi();
        echo json_encode($data);
    }

    public function get_dokter_anestesi()
    {
        $data = $this->ModelPenjadwalanOperasi->getDokterAnestesi();
        echo json_encode($data);
    }

    public function get_jenis_anestesi()
    {
        $data = $this->ModelPenjadwalanOperasi->getJenisAnestesi();
        echo json_encode($data);
    }

    public function get_ruang_rawat()
    {
        $data = $this->ModelPenjadwalanOperasi->getRuangRawat();
        echo json_encode($data);
    }

    // Data untuk tab Daftar Perjanjian Operasi
    public function get_daftar_perjanjian()
    {
        $start = $this->input->post('start');
        $length = $this->input->post('length');
        $searchValue = $this->input->post('search')['value'];
        $tanggal_mulai = $this->input->post('tanggal_mulai');
        $tanggal_akhir = $this->input->post('tanggal_akhir');
        $hari = $this->input->post('hari') ?: 'All';

        // Sorting
        $orderColumn = $this->input->post('order')[0]['column'];
        $orderDir = $this->input->post('order')[0]['dir'];
        $columns = ['no', 'tgl_operasi', 'kamar_operasi', 'tgl_dibuat', 'nama', 'norm', 'tgl_lahir_umur', 'ruang_rawat', 'diagnosis', 'tindakan', 'dokter_operator', 'dokter_anestesi', 'catatan_khusus', 'tujuan_rs'];
        $orderBy = isset($columns[$orderColumn]) && $columns[$orderColumn] != 'no' ? $columns[$orderColumn] : 'tgl_operasi';

        $result = $this->ModelPenjadwalanOperasi->getDaftarPerjanjian($start, $length, $searchValue, $tanggal_mulai, $tanggal_akhir, $hari, $orderBy, $orderDir);

        $data = array();
        $no = $start + 1;

        foreach ($result['data'] as $field) {
            $sub_array = array();
            $sub_array[] = $no;
            $sub_array[] = date('d/m/Y', strtotime($field->tgl_operasi));
            $sub_array[] = $field->kamar_operasi ?: '-';
            $sub_array[] = $field->tgl_dibuat ? date('d/m/Y H:i', strtotime($field->tgl_dibuat)) : '-';
            $sub_array[] = $field->nama;
            $sub_array[] = $field->norm;
            $sub_array[] = $field->tgl_lahir_umur;
            $sub_array[] = $field->ruang_rawat ?: '-';
            $sub_array[] = $field->diagnosis ?: '-';
            $sub_array[] = $field->tindakan ?: '-';
            $sub_array[] = $field->dokter_operator ?: '-';
            $sub_array[] = $field->dokter_anestesi ?: '-';
            $sub_array[] = $field->catatan_khusus ?: '-';
            $sub_array[] = $field->tujuan_rs ?: '-';

            // Kolom Aksi
            $aksi = '<div class="btn-group" role="group">';
            $aksi .= '<button type="button" class="btn btn-sm btn-primary btn-edit" data-id="'.$field->id_tpo.'" data-jenis="edit" title="Edit">';
            $aksi .= '<i class="fa fa-edit"></i>';
            $aksi .= '</button>';
            $aksi .= '<button type="button" class="btn btn-sm btn-danger btn-delete" data-id="'.$field->id_penjadwalan.'" title="Hapus">';
            $aksi .= '<i class="fa fa-times"></i>';
            $aksi .= '</button>';
            $aksi .= '</div>';
            $sub_array[] = $aksi;

            $data[] = $sub_array;
            $no++;
        }

        $output = array(
            "draw" => intval($this->input->post('draw')),
            "recordsTotal" => $result['recordsTotal'],
            "recordsFiltered" => $result['recordsFiltered'],
            "data" => $data
        );

        echo json_encode($output);
    }

    // Data untuk tab Operasi Hari Ini
    public function get_operasi_hari_ini()
    {
        $start = $this->input->post('start');
        $length = $this->input->post('length');
        $searchValue = $this->input->post('search')['value'];

        // Sorting
        $orderColumn = $this->input->post('order')[0]['column'];
        $orderDir = $this->input->post('order')[0]['dir'];
        $columns = ['no', 'kamar_operasi', 'tgl_dibuat', 'nama', 'norm', 'tgl_lahir_umur', 'ruang_rawat', 'diagnosis', 'tindakan', 'dokter_operator', 'dokter_anestesi', 'catatan_khusus', 'ruang_rawat_desc', 'tujuan_rs'];
        $orderBy = isset($columns[$orderColumn]) && $columns[$orderColumn] != 'no' ? $columns[$orderColumn] : 'kamar_operasi';

        $result = $this->ModelPenjadwalanOperasi->getOperasiHariIni($start, $length, $searchValue, $orderBy, $orderDir);

        $data = array();
        $no = $start + 1;

        foreach ($result['data'] as $field) {
            $sub_array = array();
            $sub_array[] = $no;
            $sub_array[] = $field->kamar_operasi ?: '-';
            $sub_array[] = $field->tgl_dibuat ? date('d/m/Y H:i', strtotime($field->tgl_dibuat)) : '-';
            $sub_array[] = $field->nama;
            $sub_array[] = $field->norm;
            $sub_array[] = $field->tgl_lahir_umur;
            $sub_array[] = $field->ruang_rawat ?: '-';
            $sub_array[] = $field->diagnosis ?: '-';
            $sub_array[] = $field->tindakan ?: '-';
            $sub_array[] = $field->dokter_operator ?: '-';
            $sub_array[] = $field->dokter_anestesi ?: '-';
            $sub_array[] = $field->catatan_khusus ?: '-';
            $sub_array[] = $field->ruang_rawat_desc ?: '-';
            $sub_array[] = $field->tujuan_rs ?: '-';

            // Kolom Aksi
            $aksi = '<div class="btn-group" role="group">';
            $aksi .= '<button type="button" class="btn btn-sm btn-primary btn-edit" data-id="'.$field->id_penjadwalan.'" data-jenis="edit" title="Edit">';
            $aksi .= '<i class="fa fa-edit"></i>';
            $aksi .= '</button>';
            $aksi .= '<button type="button" class="btn btn-sm btn-danger btn-delete" data-id="'.$field->id_penjadwalan.'" title="Hapus">';
            $aksi .= '<i class="fa fa-times"></i>';
            $aksi .= '</button>';
            $aksi .= '</div>';
            $sub_array[] = $aksi;

            $data[] = $sub_array;
            $no++;
        }

        $output = array(
            "draw" => intval($this->input->post('draw')),
            "recordsTotal" => $result['recordsTotal'],
            "recordsFiltered" => $result['recordsFiltered'],
            "data" => $data
        );

        echo json_encode($output);
    }

    // Data untuk tab History/Selesai Operasi
    public function get_history_operasi()
    {
        $start = $this->input->post('start');
        $length = $this->input->post('length');
        $searchValue = $this->input->post('search')['value'];
        $tanggal_mulai = $this->input->post('tanggal_mulai');
        $tanggal_akhir = $this->input->post('tanggal_akhir');
        $hari = $this->input->post('hari') ?: 'All';

        // Sorting
        $orderColumn = $this->input->post('order')[0]['column'];
        $orderDir = $this->input->post('order')[0]['dir'];
        $columns = ['no', 'tgl_operasi', 'kamar_operasi', 'nama', 'norm', 'tgl_lahir_umur', 'ruang_rawat', 'diagnosis', 'tindakan', 'dokter_operator', 'dokter_anestesi', 'status'];
        $orderBy = isset($columns[$orderColumn]) && $columns[$orderColumn] != 'no' ? $columns[$orderColumn] : 'tgl_operasi';

        $result = $this->ModelPenjadwalanOperasi->getHistoryOperasi($start, $length, $searchValue, $tanggal_mulai, $tanggal_akhir, $hari, $orderBy, $orderDir);

        $data = array();
        $no = $start + 1;

        foreach ($result['data'] as $field) {
            // Status operasi
            $status = '';
            if ($field->status_penjadwalan == 0) {
                $status = '<span class="badge badge-danger">Batal Operasi</span>';
            } elseif ($field->status_penjadwalan == 5) {
                $status = '<span class="badge badge-success">Operasi Selesai</span>';
            }

            $sub_array = array();
            $sub_array[] = $no;
            $sub_array[] = date('d/m/Y', strtotime($field->tgl_operasi));
            $sub_array[] = $field->kamar_operasi ?: '-';
            $sub_array[] = $field->nama . ' (' . $field->norm . ')';
            $sub_array[] = $field->tgl_lahir_umur;
            $sub_array[] = $field->ruang_rawat ?: '-';
            $sub_array[] = $field->diagnosis ?: '-';
            $sub_array[] = $field->tindakan ?: '-';
            $sub_array[] = $field->dokter_operator ?: '-';
            $sub_array[] = $field->dokter_anestesi ?: '-';
            $sub_array[] = $status;

            $data[] = $sub_array;
            $no++;
        }

        $output = array(
            "draw" => intval($this->input->post('draw')),
            "recordsTotal" => $result['recordsTotal'],
            "recordsFiltered" => $result['recordsFiltered'],
            "data" => $data
        );

        echo json_encode($output);
    }

    // Get available days in date range
    public function get_available_days()
    {
        $tanggal_mulai = $this->input->post('tanggal_mulai');
        $tanggal_akhir = $this->input->post('tanggal_akhir');

        $available_days = $this->ModelPenjadwalanOperasi->getAvailableDays($tanggal_mulai, $tanggal_akhir);

        echo json_encode($available_days);
    }

    private function _validation_rules()
    {
        return array(
            array(
                'field' => 'norm',
                'label' => 'Nomor RM',
                'rules' => 'trim|required',
                'errors' => array(
                    'required' => '%s wajib diisi',
                ),
            ),
            array(
                'field' => 'id_dokter',
                'label' => 'Dokter',
                'rules' => 'trim|required',
                'errors' => array(
                    'required' => '%s wajib diisi',
                ),
            ),
            array(
                'field' => 'tgl_operasi',
                'label' => 'Tanggal operasi',
                'rules' => 'trim|required',
                'errors' => array(
                    'required' => '%s wajib diisi',
                ),
            ),
            array(
                'field' => 'kamar_operasi',
                'label' => 'Kamar operasi',
                'rules' => 'trim|required',
                'errors' => array(
                    'required' => '%s wajib diisi',
                ),
            ),
            array(
                'field' => 'waktu_operasi',
                'label' => 'Waktu operasi',
                'rules' => 'trim|required',
                'errors' => array(
                    'required' => '%s wajib diisi',
                ),
            ),
            array(
                'field' => 'durasi_operasi',
                'label' => 'Durasi operasi',
                'rules' => 'trim|required|numeric',
                'errors' => array(
                    'required' => '%s wajib diisi',
                    'numeric' => '%s harus berupa angka',
                ),
            ),
            array(
                'field' => 'tujuan_rs',
                'label' => 'Tujuan RS',
                'rules' => 'trim|required',
                'errors' => array(
                    'required' => '%s wajib diisi',
                ),
            )
        );
    }

    // Form Penjadwalan Operasi
    public function form_penjadwalan()
    {
        $id = $this->input->post('id');
        $jenis = $this->input->post('jenis');

        // Get form data
        $dataForm = $this->ModelPenjadwalanOperasi->isiForm($jenis, $id);

        // Get dropdown data
        $data = array(
            'detail' => $dataForm,
            'dokter' => $this->ModelPenjadwalanOperasi->dokter(),
            'kamar_operasi' => $this->ModelPenjadwalanOperasi->kamar(105090104),
            'dokter_anestesi' => $this->ModelPenjadwalanOperasi->dokter_anestesi(),
            'jenis_anestesi' => $this->ModelPenjadwalanOperasi->referensi(622)
        );

        $this->load->view('PenjadwalanOperasi/FormPenjadwalan', $data);
    }

    // Delete Penjadwalan
    public function delete_penjadwalan()
    {
        $id = $this->input->post('id');

        $result = $this->ModelPenjadwalanOperasi->deletePenjadwalan($id);

        if ($result) {
            echo json_encode(array('status' => 'success', 'message' => 'Data berhasil dihapus'));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Gagal menghapus data'));
        }
    }

    // Modal Pilih Tanggal Operasi
    public function modal_pilih_tanggal()
    {
        $this->load->view('PenjadwalanOperasi/ModalPilihTanggal');
    }

    // Get Calendar Data
    public function get_calendar_data()
    {
        $start_date = $this->input->post('start_date');
        $end_date = $this->input->post('end_date');

        $result = $this->ModelPenjadwalanOperasi->getCalendarData($start_date, $end_date);

        echo json_encode($result);
    }

    // Save Penjadwalan
    public function save_penjadwalan()
    {
        $data = $this->input->post();

        $result = $this->ModelPenjadwalanOperasi->savePenjadwalan($data);

        if ($result) {
            echo json_encode(array('status' => 'success', 'message' => 'Data berhasil disimpan'));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Gagal menyimpan data'));
        }
    }
}
